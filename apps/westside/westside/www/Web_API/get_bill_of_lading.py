
import frappe
from datetime import datetime

from westside.westside.doctype.bill_of_lading.bill_of_lading import normalize_string


@frappe.whitelist(allow_guest=True)
def bol_filter_carrier_data():
    try:

        carriers = frappe.get_all(
            "Carrier", fields=["partyalias", "partyname1", "name"])

        return {
            "status": "success",
            "carriers": carriers,

        }
    except Exception as e:
        frappe.log_error(
            f"Error fetching booking data: {str(e)}", "Booking API Error")
        return {
            "status": "error",
            "message": str(e)
        }
    


@frappe.whitelist(allow_guest=True)
def bol_filter_consignee_data():
    try:
        
        customers = frappe.get_all("Customer DB", fields=["name","customer_name","first_name", "last_name"])

        return {
            "status": "success",
            "consignee": customers,

        }
    except Exception as e:
        frappe.log_error(
            f"Error fetching booking data: {str(e)}", "Booking API Error")
        return {
            "status": "error",
            "message": str(e)
        }


@frappe.whitelist(allow_guest=True)
def get_bill_of_lading(consignee=None, port_of_loading=None, carrier=None, page=1, limit=20):
    """
    Get Bill of Lading documents (cleaned format) with optional filters and pagination.
    - Filters: Consignee, Port of Loading, Carrier
    - Pagination: Defaults to 20 per page, accepts 'page' param
    """

    try:

        doctype = "Bill of Lading"
        filters = []

        # Carrier filter
        if carrier:
            carrier_record = None

            # Case 1: Carrier passed as primary key
            if frappe.db.exists("Carrier", carrier):
                carrier_record = frappe.get_value(
                    "Carrier",
                    carrier,
                    ["partyalias", "partyname1"],
                    as_dict=True
                )
            else:
                # Case 2: Carrier passed as text
                carrier_record = frappe.get_value(
                    "Carrier",
                    {"partyalias": ["like", f"%{carrier}%"]},
                    ["partyalias", "partyname1"],
                    as_dict=True
                ) or frappe.get_value(
                    "Carrier",
                    {"partyname1": ["like", f"%{carrier}%"]},
                    ["partyalias", "partyname1"],
                    as_dict=True
                )

            # Case 3: Fallback to Partner Information
            if not carrier_record:
                bol_names = frappe.get_all(
                    "Partner Information",
                    filters={
                        "partner_role": "Carrier",
                        "partner_name": ["like", f"%{carrier}%"]
                    },
                    pluck="parent"
                )
                if bol_names:
                    filters.append(["name", "in", bol_names])
                else:
                    frappe.local.response.update({
                        "status_code": 404,
                        "error": f"No Carrier found matching '{carrier}'"
                    })
                    return
            else:
                carrier_values = [carrier_record.partyalias, carrier_record.partyname1]
                carrier_values = [c for c in carrier_values if c]
                filters.append(["carrier", "in", carrier_values])

        
        if consignee:
            bol_names = []
            customer_name = None

            # Frontend always sends PK, so check if it exists in Customer DB
            if frappe.db.exists("Customer DB", consignee):
                customer_name = frappe.get_value("Customer DB", consignee, "customer_name")

                # Search Partner Information:
                # 1. Match partner_table_name with PK (new records)
                # 2. OR match partner_name with customer_name (old records)
                bol_names = frappe.get_all(
                    "Partner Information",
                    filters={
                        "partner_role": "Consignee"
                    },
                    or_filters=[
                        {"partner_table_name": consignee},
                        {"partner_name": ["like", f"%{customer_name}%"]}
                    ],
                    pluck="parent"
                )

            if not bol_names:
                frappe.local.response.update({
                    "status_code": 404,
                    "error": f"No Consignee found matching '{consignee}'"
                })
                return

            filters.append(["name", "in", bol_names])



        if port_of_loading: 
            filters.append(["port_of_load", "=", port_of_loading])


        offset = (int(page) - 1) * int(limit)

        # Fetch only names with filters and pagination
        int_count = frappe.db.count(doctype, filters=filters)
        docs = frappe.get_all(
            doctype,
            filters=filters,
            fields=["name"],
            start=offset,
            page_length=int(limit),
            order_by="modified desc"
        )

        results = []
        excluded_prefixes = ("section_break_", "column_break_")
        excluded_fields = {
            "doctype", "owner", "creation", "modified", "modified_by", "docstatus","xml_data","xml_file_name","master_data_section"
        }

        for d in docs:
            doc = frappe.get_doc(doctype, d.name)
            clean_data = {}
            clean_data["name"] = doc.name

            for df in doc.meta.fields:
                fieldname = df.fieldname
                if (
                    fieldname in excluded_fields
                    or any(fieldname.startswith(prefix) for prefix in excluded_prefixes)
                ):
                    continue

                if df.fieldtype == "Table":
                    pass
                else:
                    clean_data[fieldname] = doc.get(fieldname)

                docket_info = frappe.get_value(
                    "Docket DB",
                    {"blno": clean_data.get("bol_number")},
                    ["name", "status"],
                    as_dict=True
                )

                if docket_info:
                    clean_data["docket_id"] = docket_info.name
                    clean_data["docket_status"] = docket_info.status
                else:
                    clean_data["docket_id"] = None
                    clean_data["docket_status"] = None

                if doc.parties:
                    for party in doc.parties:
                        if party.partner_role == "Consignee":
                            clean_data["consignee"] = party.partner_name
                            break

                # ETA
                clean_data["eta"] = ""
                booking_request = frappe.get_value(
                    "Booking Request",
                    {"carrier_booking_number": doc.carrier_booking_number},
                    ["name"],
                    as_dict=True
                )  
                if booking_request:     
                    lst_main_carriage = frappe.get_all(
                        "Booking Main Carriage", 
                        filters={"parent": booking_request.name}, 
                        fields=["port_of_discharge", "etd", "eta"],
                        order_by="etd asc"
                    )
                    lst_main_carriage = sorted(
                        lst_main_carriage,
                        key=lambda x: x.get("etd") or datetime.min
                    )
                    if lst_main_carriage:
                        clean_data["eta"] = lst_main_carriage[-1].get("eta") or ""

            # 🔹 Add latest attachments 
            attachments = []
            if doc.bol_attachments:
                sorted_attachments = sorted(
                    doc.bol_attachments,
                    key=lambda x: (x.order or 0, x.modified or datetime.min),
                    reverse=True
                )
                latest = sorted_attachments[0]  
                attachments.append({
                    "file_name": latest.file_name,
                    "file_url": latest.file_url,
                    "order": latest.order
                })

            clean_data["attachments"] = attachments

            results.append(clean_data)

        frappe.local.response["status_code"] = 200
        frappe.local.response["message"] = results
        frappe.local.response["total_count"] = int(int_count)
        frappe.local.response["page"] = int(page)
        frappe.local.response["limit"] = int(limit)

    except Exception as e:
        frappe.local.response["status_code"] = 500
        frappe.local.response["error"] = f"Failed processing: {str(e)}"
        raise





@frappe.whitelist(allow_guest=True)
def get_details_bill_of_lading(bol_id=None):
    """
    Get Bill of Lading documents (cleaned format) with optional filters and pagination.
    - Filters: Consignee, Port of Loading, Carrier
    - Pagination: Defaults to 20 per page, accepts 'page' param
    """

    try:

        doctype = "Bill of Lading"
        results = []
        excluded_prefixes = ("section_break_", "column_break_")
        excluded_fields = {
            "doctype", "owner", "creation", "modified", "modified_by", "docstatus","xml_data","xml_file_name","master_data_section"
        }

        try:
            update_bol_parties_name(bol_id)
        except Exception as e:
            frappe.log_error(frappe.get_traceback(), "Error in update_bol_parties_name")
        doc = frappe.get_doc(doctype, bol_id)
        clean_data = {}
        clean_data["name"] = doc.name

        for df in doc.meta.fields:
            fieldname = df.fieldname

            if (
                fieldname in excluded_fields
                or any(fieldname.startswith(prefix) for prefix in excluded_prefixes)
            ):
                continue

            if fieldname == "total_gross_weight":
                    value = doc.get(fieldname)
                    if value is not None:
                        try:
                            doc.set(fieldname, "{:.2f}".format(float(value)))
                        except (ValueError, TypeError):
                            pass 
		
            if df.fieldtype == "Table":
                child_items = doc.get(fieldname)
                clean_data[fieldname] = [
                    {k: v for k, v in item.as_dict().items()
                        if k not in (
                        "doctype", "parent", "parentfield", "parenttype", "idx",
                        "owner", "modified", "modified_by", "docstatus",
                        "section_break_oukm", "column_break_ojnf"
                    )}
                    for item in child_items
                ]
            else:
                clean_data[fieldname] = doc.get(fieldname)

            docket_info = frappe.get_value(
                "Docket DB",
                {"blno": clean_data.get("bol_number")},
                ["name", "status"],
                as_dict=True
            )

            if docket_info:
                clean_data["docket_id"] = docket_info.name
                clean_data["docket_status"] = docket_info.status
            else:
                clean_data["docket_id"] = None
                clean_data["docket_status"] = None
        
        lst_equipment = []
        lst_cargo = []
        total_net_weight_caluclated = 0
        if frappe.db.exists("Equipments", {"bill_of_lading_id": bol_id}):
            equipment_docs = frappe.get_all(
                "Equipments",
                filters={"bill_of_lading_id": bol_id},
                fields=["name"]  
            )

            for eqp in equipment_docs if equipment_docs else []:
                full_doc = frappe.get_doc("Equipments", eqp.name)
                if full_doc: 
                    total_net_weight_caluclated += full_doc.cargo[0].get("net_weight") if full_doc.cargo else 0
                    dct_equipment = {
                        "name": full_doc.name,
                        "equipment_name": full_doc.equipment_name,
                        "code_value": full_doc.code_value,
                        "equipment_type": frappe.get_value("Container Type", full_doc.container_type_id, "shortdescription"),
                        "description": full_doc.description,
                        "comment": full_doc.comment,
                        "shipper_seal_number": full_doc.shipper_seal_number,
                        "carrier_seal_number": full_doc.carrier_seal_number,
                        "weight_type": full_doc.weight_type,
                        "weight_value": full_doc.weight_value,
                        "cargo_gross_weight": full_doc.cargo[0].get("cargo_gross_weight") if full_doc.cargo else None,
                        "gross_volume": full_doc.cargo[0].get("gross_volume") if full_doc.cargo else None,
                        "volume_unit": full_doc.cargo[0].get("volume_unit") if full_doc.cargo else None,
                    }
                    lst_equipment.append(dct_equipment)
                    dct_cargo = {
                        "hs_code": full_doc.cargo[0].get("hs_code") if full_doc.cargo else None,
                        "cargo_description": full_doc.cargo[0].get("cargo_description") if full_doc.cargo else None,
                        "cargo_gross_weight": full_doc.cargo[0].get("cargo_gross_weight") if full_doc.cargo else None,
                        "gross_volume": full_doc.cargo[0].get("gross_volume") if full_doc.cargo else None,
                        "volume_unit": full_doc.cargo[0].get("volume_unit") if full_doc.cargo else None,
                        "package_count": full_doc.cargo[0].get("package_count") if full_doc.cargo else None,
                        "package_type_description": full_doc.cargo[0].get("package_type_description") if full_doc.cargo else None,
                        "package_counttype_outermost": full_doc.cargo[0].get("package_counttype_outermost") if full_doc.cargo else None
                    }
                    lst_cargo.append(dct_cargo)
        clean_data["equipment"] = lst_equipment
        clean_data["cargo"] = lst_cargo
        clean_data["total_net_weight_caluclated"] = ''
        clean_data["weight_difference_warning"] = ''
        if total_net_weight_caluclated:
            clean_data["total_net_weight_caluclated"] = "{:.2f}".format(total_net_weight_caluclated)
            dbl_weight_difference = abs(float(clean_data.get("total_gross_weight") or 0) - (float(total_net_weight_caluclated) or 0) )
            if  dbl_weight_difference > 0:
                clean_data["weight_difference_warning"] = f"There is a difference of {round(dbl_weight_difference,2)} KG in the total gross weight and the calculated total net weight of the equipments. Please check the equipment details."
        clean_data["total_equipment"] = len(lst_equipment)

        results.append(clean_data)

        frappe.local.response["status_code"] = 200
        frappe.local.response["message"] = results
        

    except Exception as e:
        frappe.local.response["status_code"] = 500
        frappe.local.response["error"] = f"Failed processing: {str(e)}"
        raise



def update_bol_parties_name(bol_id = None):
    try:
        if not bol_id:
            return
        role_to_doctype = {
            "Shipper": ("Shipper", "shipper_name"),
            "Consignee": ("Customer DB","customer_name"),
            "Carrier": ("Carrier", "partyname1"),
            "ContractParty": ("Shipper", "shipper_name"),
            "NotifyParty": ("Customer DB","customer_name"),
            "FreightPayer": ("Shipper", "shipper_name"),
        }

        doc_bol = frappe.get_doc("Bill of Lading", bol_id)
        for party in doc_bol.parties:
            if (party.partner_table_name or "").strip():
                continue
            role = party.partner_role
            doctype_info = role_to_doctype.get(role)

            if not doctype_info:
                continue  

            doctype, fieldname = doctype_info
            base_name = (party.partner_name or "")[:6]
            str_search_string = normalize_string(base_name)

            partner_id = frappe.get_value(
                doctype,
                {fieldname: ["like", f"%{str_search_string}%"]},
                "name"
            )
            if not partner_id:
                base_name = (party.partner_name or "")[:3]
                str_search_string = normalize_string(base_name)

                partner_id = frappe.get_value(
                    doctype,
                    {fieldname: ["like", f"%{str_search_string}%"]},
                    "name"
                )

            if partner_id:
                frappe.db.set_value("Partner Information", party.name, "partner_table_name", partner_id)
                frappe.db.commit()
        
        return True

    except Exception as e:
        frappe.local.response["status_code"] = 500
        frappe.local.response["error"] = f"Failed processing: {str(e)}"
        raise