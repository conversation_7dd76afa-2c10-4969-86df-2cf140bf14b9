# Copyright (c) 2025, faircode and contributors
# For license information, please see license.txt

from datetime import datetime
import frappe
from frappe.model.document import Document
import os,re
from bs4 import BeautifulSoup
import json

from westside.westside.doctype.booking_request.booking_request import parse_date_field


UOM_MAP = {
	"KGM": "KG",
	"KGS": "KGM",
	"LBS": "LBS",
	"CBM": "CBM",
	"MTQ": "MTQ",
}

class BillofLading(Document):
	pass

@frappe.whitelist()
def normalize_string(s):
	"""Remove spaces, special chars, lowercase"""
	return re.sub(r'[^a-z0-9]', '', s.lower())


@frappe.whitelist(allow_guest=True)
def process_webbl_exml_files(lst_webbl_files):
	"""
	Process a list of webbl XML files, extracting relevant data and creating Bill of Lading documents.
	"""
	
	try:
		for file_name in lst_webbl_files:
			try:
				file_path = frappe.utils.get_site_path("private", "sftp_downloads", file_name)
				if not os.path.exists(file_path):
					frappe.throw(f"File {file_name} does not exist at {file_path}")
						
				json_output = convert_xml_to_json(file_path)			
				dct_data = json.loads(json_output)

				# print(dct_data,'main')

				temp_data = {"doctype": "Bill of Lading"}

				msg_header = (dct_data.get("Message") or {}).get("Header", {})
				msg_body = (dct_data.get("Message") or {}).get("MessageBody", {})
				props = msg_body.get("MessageProperties", {}) or {}
				
				# Reference Information
				references = props.get("ReferenceInformation", []) or []
				ref_map = {
					r.get("ReferenceType"): r.get("text")
					for r in references
					if r.get("ReferenceType") and r.get("text")
				}
				
				bol_number = ref_map.get("BillOfLadingNumber")
				# Dates
				dates_raw = props.get("DateTime", []) or []
				dates = [dates_raw] if isinstance(dates_raw, dict) else dates_raw
				get_date_by_type = lambda dtype: next(
					(parse_date_field(d) for d in dates if isinstance(d, dict) and d.get("DateType") == dtype),
					None
				)

				# Document info
				temp_data.update({
					"document_identifier": msg_header.get("DocumentIdentifier"),
					"document_date": get_date_by_type("Message"),
					"create_date_time": get_date_by_type("Message"),
					"shipped_on_board_date": get_date_by_type("ShippedOnBoard"),
					"received_for_shipment": get_date_by_type("ReceivedForShipment"),
					"xml_file_name": file_name,
					"xml_data":json_output
				})
				
				# BL Release Info
				bl_location = (props.get("BlLocations") or {}).get("Location") or {}
				if not isinstance(bl_location,list):
					bl_location = [bl_location]
				for bl_loc in bl_location:
					if bl_loc.get("LocationType") == "BillOfLadingRelease":
						temp_data["bol_release_date"] = parse_date_field(bl_loc.get("DateTime") or {})
						location_code = (bl_loc.get("LocationCode") or {}).get("text")
						temp_data["bol_release_location"] = location_code if location_code else None
						
					elif bl_loc.get("LocationType") == "FreightPaymentLocation":
						temp_data["freight_payment_date"] = parse_date_field(bl_loc.get("DateTime") or {})
						freight_payment_location = (bl_loc.get("LocationCode") or {}).get("text")
						temp_data["freight_payment_location"] = freight_payment_location if freight_payment_location else None
						

				# Reference fields
				temp_data.update({
					"bol_number": ref_map.get("BillOfLadingNumber"),
					"carrier_booking_number": ref_map.get("BookingNumber"),
					"contract_number": ref_map.get("ShipperIdentifyingNumber"),
				})

				# Shipment ID
				shipment_id_block = props.get("ShipmentID") or {}
				shipment_id = (shipment_id_block.get("ShipmentIdentifier") or {})
				temp_data.update({
					"document_version": shipment_id_block.get("DocumentVersion"),
					"message_status": shipment_id.get("MessageStatus"),
					"message_type": (msg_header.get("MessageType") or {}).get("text"),
					"shipment_id": shipment_id.get("text"),
				})

				shipment_decleared_value = props.get("ShipmentDeclaredValue") or {}
				temp_data.update({
					"shipment_declared_amount": shipment_decleared_value.get("text"),
					"shipment_declared_currency": shipment_decleared_value.get("Currency"),
					"letter_of_credit_number": (props.get("ShipmentDeclaredValue") or {}).get("text")
				})
				letter_of_credit_number = (props.get("LetterOfCreditDetails") or {})
				temp_data.update({
					"letter_of_credit_number": letter_of_credit_number.get("LetterOfCreditNumber") or None
				})

				export_licence = (props.get("ExportLicenseDetails") or {})
				licence_date_by_type = lambda dtype: next(
					(parse_date_field(d) for d in (export_licence.get("DateTime") or {}) if (d or {}).get("DateType") == dtype), None
				)
				temp_data.update({
					"export_license_number" : export_licence.get("ExportLicenseNumber"),
					"export_license_issue" : licence_date_by_type("IssueDate"),
					"export_license_expiry" : licence_date_by_type("ExpiryDate"),
				})

				# Haulage & Transport Details
				haulage = props.get("HaulageDetails") or {}
				transportation = props.get("TransportationDetails") or {}
				if transportation and isinstance(transportation,dict):
					transportation = [transportation]

				for trans in transportation:
					if trans.get("TransportStage") == "Main":
						transportation = trans
				

				conveyance = transportation.get("ConveyanceInformation") or {} if isinstance(transportation, dict) else {}
				transport_means = conveyance.get("TransportMeans") or {} if isinstance(conveyance, dict) else {}
				temp_data.update({
					"movement_type": haulage.get("MovementType"),
					"service_type": haulage.get("ServiceType"),
					"transport_mode": transportation.get("TransportMode"),
					"transport_stage": transportation.get("TransportStage"),
					"main_voyage": conveyance.get("VoyageTripNumber"),
					"main_vessel": conveyance.get("ConveyanceName"),
					"lloyds_code": (conveyance.get("TransportIdentification") or {}).get("text"),
					"transport_means_type": transport_means.get("TransportMeansType"),
					"transport_means": transport_means.get("text"),
					"carrier": conveyance.get("CarrierSCAC"),
					"carrier_table": frappe.get_value("Carrier", {"partyalias": conveyance.get("CarrierSCAC")}, "name")
				})

				traspotation_locations = transportation.get("Location") or []
				location_field_map = {
					"PortOfLoading": "port_of_load",
					"PlaceOfDelivery": "place_of_delivery",
					"PlaceOfReceipt": "place_of_receipt",
					"PortOfDischarge": "port_of_discharge",
					"PortOfLoadingLocName": "port_of_load_location",
					"PlaceOfDeliveryLocName": "place_of_delivery_location",
					"PlaceOfReceiptLocName": "place_of_receipt_location",
					"PortOfDischargeLocName": "port_of_discharge_location",
				}
				for loc in traspotation_locations:
					location_type = loc.get("LocationType")
					location_name = loc.get("LocationName") or None
					location_code = (loc.get("LocationCode") or {}).get("text")
					if location_type and location_code:
						field_name = location_field_map.get(location_type)
						if field_name:
							temp_data[field_name] = location_code
					if location_name:
						field_name = location_field_map.get(f"{location_type}LocName")
						if field_name:
							temp_data[field_name] = location_name




				# Control Totals
				control = props.get("ControlTotal") or {}
				temp_data.update({
					"total_equipment": control.get("NumberOfEquipment",None),
					"total_packages": control.get("NumberOfPackages",None),
					"total_gross_weight": (control.get("GrossWeight") or {}).get("text"),
					"total_gross_volume": (control.get("GrossVolume") or {}).get("text"),
				})

				existing_bol = frappe.get_value("Bill of Lading", {"bol_number": bol_number}, "name")

				try:
					if existing_bol:
						existing_status = frappe.get_value("Bill of Lading", existing_bol, "created_status")
						if existing_status == "S":
							temp_data["created_status"] = "O"
						else:
							temp_data["created_status"] = "C"
				except Exception:
					frappe.log_error(
						frappe.get_traceback(),
						f"Error occurred while setting created_status for BOL {bol_number}"
					)
					pass

				
				frappe.db.delete(
					"Reference Information",
					filters={"parent": existing_bol, "parentfield": "references"}
				)
				temp_data["references"] = []
				for refe in props.get('ReferenceInformation',{}):
					dct_reff ={
						"reference_type": refe.get("ReferenceType"),
						"text":refe.get("text")

					}
					temp_data["references"].append(dct_reff)

				frappe.db.delete(
					"Instructions",
					filters={"parent": existing_bol, "parentfield": "instructions"}
				)
				temp_data["instructions"] = []
				for comment in (props.get('Instructions') or {}).get("ShipmentComments",[]):
					dct_comment = {
						"main_reference": "Shipment Comments",
						"comment_type": comment.get("CommentType"),
						"clause_type":comment.get('ClauseType'),
						"text":comment.get('text')
					}
					temp_data["instructions"].append(dct_comment)
				partner_data = (props.get("Parties") or  {}).get("PartnerInformation", [])
				if partner_data and isinstance(partner_data, dict):
					partner_data = [partner_data]
				for partner in partner_data:
					if partner.get("PartnerRole") == "Carrier":
						temp_data['carrier'] = (partner.get("PartnerIdentifier") or {}).get("text")
						break
				frappe.db.delete(
					"Partner Information",
					filters={"parent": existing_bol, "parentfield": "parties"}
				)

				temp_data["parties"] = []
				for partner in partner_data:
					
					partner_role = safe_text(partner.get("PartnerRole"))
					partner_name = safe_text(partner.get("PartnerName"))
					partner_identifier = safe_text((partner.get("PartnerIdentifier") or {}).get("text"))

					contact_info = partner.get("ContactInformation") or {}
					contact_name = safe_text(contact_info.get("ContactName"))
					communication = contact_info.get("CommunicationValue")

					partner_table_name = ""
					try:
						if partner.get("PartnerRole") == "Carrier":
							partner_name_norm = normalize_string(partner_name or "")
							carriers = frappe.get_all("Carrier", fields=["name", "partyname1"])
							for carrier in carriers:
								db_value_norm = normalize_string(carrier.get("partyname1") or "")
								if partner_name_norm == db_value_norm:
									partner_table_name = carrier.get("name")
						
						if partner.get("PartnerRole") == "Consignee":
							partner_name_norm = normalize_string(partner_name or "")
							customers = frappe.get_all("Customer DB", fields=["name", "customer_name"])
							for customer in customers:
								db_value_norm = normalize_string(customer.get("customer_name") or "")
								if partner_name_norm == db_value_norm:
									partner_table_name = customer.get("name")
							
						if partner.get("PartnerRole") == "Shipper":
							partner_name_norm = normalize_string(partner_name or "")
							shippers = frappe.get_all("Shipper", fields=["name", "shipper_name"])
							for shipper in shippers:
								db_value_norm = normalize_string(shipper.get("shipper_name") or "")
								if partner_name_norm == db_value_norm:
									partner_table_name = shipper.get("name")

						if partner.get("PartnerRole") == "NotifyParty":
							partner_name_norm = normalize_string(partner_name or "")
							customers = frappe.get_all("Customer DB", fields=["name", "customer_name"])
							for customer in customers:
								db_value_norm = normalize_string(customer.get("customer_name") or "")
								if partner_name_norm == db_value_norm:
									partner_table_name = customer.get("name")
							
						if partner.get("PartnerRole") == "ContractParty":
							partner_name_norm = normalize_string(partner_name or "")
							shippers = frappe.get_all("Shipper", fields=["name", "shipper_name"])
							for shipper in shippers:
								db_value_norm = normalize_string(shipper.get("shipper_name") or "")
								if partner_name_norm == db_value_norm:
									partner_table_name = shipper.get("name")

						if partner.get("PartnerRole") == "FreightPayer":
							partner_name_norm = normalize_string(partner_name or "")
							shippers = frappe.get_all("Shipper", fields=["name", "shipper_name"])
							for shipper in shippers:
								db_value_norm = normalize_string(shipper.get("shipper_name") or "")
								if partner_name_norm == db_value_norm:
									partner_table_name = shipper.get("name")

					except:
						frappe.log_error(f"Error getting partner table name: {partner_name}", "Partner Table Name Error")
						pass

					telephone = email = ""
					if isinstance(communication, list):
						for c in communication:
							if c.get("CommunicationType") == "Telephone":
								telephone = safe_text(c.get("text"))
							elif c.get("CommunicationType") == "Email":
								email = safe_text(c.get("text"))
					elif isinstance(communication, dict):
						if communication.get("CommunicationType") == "Telephone":
							telephone = safe_text(communication.get("text"))
						elif communication.get("CommunicationType") == "Email":
							email = safe_text(communication.get("text"))

					address = (partner.get("AddressInformation") or {}).get("AddressLine")
					address_line = safe_text(address, separator="\n")

					dct_parties = {
						"partner_role": partner_role,
						"partner_name": partner_name,
						"partner_identifier": partner_identifier,
						"contact_name": contact_name,
						"telephone": telephone,
						"email": email,
						"address_line": address_line,
						"doctype": "Partner Information",
						"partner_table_name": partner_table_name
					}

					temp_data["parties"].append(dct_parties)



				if existing_bol:
					bol_doc = frappe.get_doc("Bill of Lading", existing_bol)
					bol_doc.update(temp_data)
					bol_doc.save(ignore_permissions=True)
				else:
					temp_data["doctype"] = "Bill of Lading"
					temp_data["bol_number"] = bol_number
					temp_data["created_status"] = "C"
					frappe.get_doc(temp_data).insert(ignore_permissions=True)
				frappe.db.commit()
				temp_data_update = {}
				doc_bol_id = frappe.get_value("Bill of Lading", {"bol_number": bol_number}, "name")
				
				lst_equpments = msg_body.get("MessageDetails", {}).get("EquipmentDetails", [])
				if isinstance(lst_equpments, dict):
					lst_equpments = [lst_equpments]
				for equipment in lst_equpments:
					equipment_id = equipment.get("EquipmentIdentifier")
					if not equipment_id:
						continue
					doc_name = frappe.db.get_value(
						"Equipments",
						{
							"carrier_booking_number": ref_map.get("BookingNumber"),
							"equipment_name": equipment_id
						}
					)
					if not doc_name:
						eq_type = equipment.get("EquipmentType", {})
						eq_weight = equipment.get("EquipmentGrossWeight", {})
						eq_volume = equipment.get("EquipmentGrossVolume", {})
						seal_info = equipment.get("EquipmentSeal", {})
						seal_party = seal_info.get("SealingParty") if seal_info else None
						seal_text = seal_info.get("text") if seal_info else ""
						eqp_description = eq_type.get("EquipmentDescription")
						if not eqp_description:
							eqp_description = frappe.get_value("Container Type", {"typecode": eq_type.get("EquipmentTypeCode")}, "shortdescription")

						try:
							int_container_type_id = frappe.get_value("Container Type", {"typecode": eq_type.get("EquipmentTypeCode")}, "name")
						except:
							int_container_type_id = None

						equp_temp_data = {
							"doctype": "Equipments",
							"equipment_name": equipment_id,
							"bill_of_lading_id": doc_bol_id,
							"carrier_booking_number": ref_map.get("BookingNumber"),
							"inttra_booking_number": ref_map.get("INTTRABookingNumber"),
							"code_value": eq_type.get("EquipmentTypeCode"),
							"container_type_id": int_container_type_id,
							"description": eqp_description,
							"comment": equipment.get("EquipmentComment", {}).get("text"),
							"shipper_seal_number": seal_text if seal_party == "Shipper" else "",
							"carrier_seal_number": seal_text if seal_party == "Carrier" else "",
							"weight_value": eq_weight.get("text"),
							"weight_type": eq_weight.get("UOM"),
							"gross_volume": eq_volume.get("text"),
							"volume_unit": eq_volume.get("UOM"),
							"cargo_weight": eq_weight.get("text"),
							"gross_weight": eq_weight.get("text"),
						}

						frappe.get_doc(equp_temp_data).insert(ignore_permissions=True)
						frappe.db.commit()
					else:
						
						doc_equipment = frappe.get_doc("Equipments", doc_name)
						seal_info = equipment.get("EquipmentSeal", {})
						eqp_description = equipment.get("EquipmentType", {}).get("EquipmentDescription")
						if not eqp_description:
							eqp_description = frappe.get_value("Container Type", {"typecode": equipment.get("EquipmentType", {}).get("EquipmentTypeCode")}, "shortdescription")
						try:
							int_container_type_id = frappe.get_value("Container Type", {"typecode": equipment.get("EquipmentType", {}).get("EquipmentTypeCode")}, "name")
						except:
							int_container_type_id = None
			
						doc_equipment.update({
							"equipment_name": equipment_id,
							"code_value": equipment.get("EquipmentType", {}).get("EquipmentTypeCode"),
							"container_type_id": int_container_type_id,
							"description": eqp_description,
							"comment": equipment.get("EquipmentComment", {}).get("text"),
							"shipper_seal_number": seal_info.get("text") if seal_info.get("SealingParty") == "Shipper" else "",
							"carrier_seal_number": seal_info.get("text") if seal_info.get("SealingParty") == "Carrier" else "",
							"weight_value": equipment.get("EquipmentGrossWeight", {}).get("text"),
							"weight_type": equipment.get("EquipmentGrossWeight", {}).get("UOM"),
							"gross_volume": equipment.get("EquipmentGrossVolume", {}).get("text"),
							"volume_unit": equipment.get("EquipmentGrossVolume", {}).get("UOM"),
							"cargo_weight": equipment.get("EquipmentGrossWeight", {}).get("text"),
							"gross_weight": equipment.get("EquipmentGrossWeight", {}).get("text"),
							"bill_of_lading_id": doc_bol_id,
						}) 
						doc_equipment.save()
						frappe.db.commit()
				
				rst_goods_details = msg_body.get("MessageDetails", {}).get('GoodsDetails',{})
				if isinstance(rst_goods_details,dict):	
					lst_goods_details = [rst_goods_details]
				else:
					lst_goods_details = rst_goods_details
				for goods in lst_goods_details:				                        
					split_goods_list = goods.get("SplitGoodsDetails", [])
					if isinstance(split_goods_list, dict):
						split_goods_list = [split_goods_list]
					if not split_goods_list:
						continue
					

					for split in split_goods_list:
						doc_name = frappe.db.get_value(
							"Equipments",
							{
								"carrier_booking_number": ref_map.get("BookingNumber"),
								"equipment_name": split.get("EquipmentIdentifier")
							}
						)
						if not doc_name:
							continue
						

						raw_unit = split.get("SplitGoodsGrossWeight", {}).get("UOM", "")
						net_weight_unit = UOM_MAP.get(raw_unit.upper(), raw_unit.upper())
						
						doc_equipment = frappe.get_doc("Equipments", doc_name)
						if isinstance(goods.get("ProductId"), list):
							hs_code = [c.get("text") for c in goods.get("ProductId") if c.get("ItemTypeIdCode") == "HarmonizedSystem" and c.get("text")]
							hs_code = hs_code[0] if hs_code else "400400"
							schedule_b = [c.get("text") for c in goods.get("ProductId") if c.get("ItemTypeIdCode") == "USCensusScheduleB" and c.get("text")]
							schedule_b = schedule_b[0] if schedule_b else ""
						elif isinstance(goods.get("ProductId"), dict):	
							hs_code = goods.get("ProductId", {}).get("text", "") if goods.get("ProductId", {}).get("ItemTypeIdCode") == "HarmonizedSystem" else "400400"
							schedule_b = goods.get("ProductId", {}).get("text", "") if goods.get("ProductId", {}).get("ItemTypeIdCode") == "USCensusScheduleB" else ""
						else:
							hs_code = "400400"
							schedule_b = ""

						existing_row = None
						for row in doc_equipment.cargo:
							if row.hs_code == hs_code:
								existing_row = row
								break
						
						comments = goods.get("PackageDetailComments", [])
						if isinstance(comments, dict):
							comments = [comments]

						marks = goods.get("PackageMarks", {}).get("Marks")
						if isinstance(marks, list):
							marks = "\n".join([str(m) for m in marks if m])  
						str_marks_and_numbers = marks
						
						temp_data_update = {
							"hs_code": hs_code,
							"hs_code_description": ", ".join([c.get("text") for c in comments if c.get("text")])

						}
						if existing_row:
							existing_row.hs_code = hs_code
							existing_row.cargo_description = ", ".join([c.get("text") for c in comments if c.get("text")])
							existing_row.cargo_weight = split.get("SplitGoodsGrossWeight", {}).get("text")
							existing_row.net_weight = split.get("SplitGoodsGrossWeight", {}).get("text")
							existing_row.net_weight_unit = net_weight_unit
							existing_row.gross_volume = split.get("SplitGoodsGrossVolume", {}).get("text")
							existing_row.gross_volume_unit = split.get("SplitGoodsGrossVolume", {}).get("UOM")
							existing_row.cus_code = hs_code
							existing_row.package_counttype_outermost = frappe.get_value("Package Type", {"xmlcode": goods.get("PackageDetail", {}).get("PackageTypeCode")}, "name")
							existing_row.package_count = split.get("SplitGoodsNumberOfPackages")
							existing_row.ncm_codes = hs_code
							existing_row.marks_and_numbers = str_marks_and_numbers
							existing_row.origin_of_goods = ""
							existing_row.schedule_b_number = schedule_b
							existing_row.print_on_bl_as = ""
							existing_row.save()
							frappe.db.commit()

						else:						
							cargo_row = doc_equipment.append("cargo", {})
							cargo_row.hs_code = hs_code
							cargo_row.cargo_description = ", ".join([c.get("text") for c in comments if c.get("text")])
							cargo_row.cargo_weight = split.get("SplitGoodsGrossWeight", {}).get("text")
							cargo_row.net_weight = split.get("SplitGoodsGrossWeight", {}).get("text")
							cargo_row.net_weight_unit = net_weight_unit
							cargo_row.gross_volume = split.get("SplitGoodsGrossVolume", {}).get("text")
							cargo_row.gross_volume_unit = split.get("SplitGoodsGrossVolume", {}).get("UOM")
							cargo_row.cus_code = hs_code
							cargo_row.package_counttype_outermost = frappe.get_value("Package Type", {"xmlcode": goods.get("PackageDetail", {}).get("PackageTypeCode")}, "name")
							cargo_row.package_count = split.get("SplitGoodsNumberOfPackages")
							cargo_row.ncm_codes = hs_code
							cargo_row.marks_and_numbers = str_marks_and_numbers
							cargo_row.origin_of_goods = ""
							cargo_row.schedule_b_number = schedule_b
							cargo_row.print_on_bl_as = ""

							doc_equipment.save()
							frappe.db.commit()

						

				bol_update_doc = frappe.get_doc("Bill of Lading", doc_bol_id)
				bol_update_doc.update(temp_data_update)
				bol_update_doc.save(ignore_permissions=True)
				frappe.db.commit()
				try:
					frappe.db.set_value("SFTP Downloaded Files", {"file_name": file_name}, "status", "Processed")
					frappe.db.set_value("SFTP Downloaded Files", {"file_name": file_name}, "connected_doctype_id", doc_bol_id)
					frappe.db.commit()
				except:
					pass
				
			except Exception as e:
				frappe.db.rollback()
				frappe.log_error(frappe.get_traceback(), f"Error processing the BOL xml file: {file_name}")
				continue
			
			return {
				"message": "BOL processed successfully",
				"status": "success",
				"status_code": 200,
				"bol_id": doc_bol_id
			}

	except Exception as e:
		frappe.db.rollback()
		frappe.log_error(frappe.get_traceback(), f"Error processing {file_name}")
		frappe.msgprint(f"Failed to process {file_name}: {str(e)}")
		frappe.local.response["message"] = f"Failed to process {file_name}: {str(e)}"
		return False



@frappe.whitelist(allow_guest=True)
def bill_of_lading_manual():
	"""
	Create or update a Bill of Lading document based on provided payload data in the request.
	"""
	try:
		dct_payload_data = json.loads(frappe.form_dict.get("data"))
		if not dct_payload_data:
			frappe.throw("No JSON payload found in request")

		if not dct_payload_data.get("master_data").get("bol_number"):
			frappe.throw("Field 'bol_number' is required")

		existing_bol = frappe.get_value(
			"Bill of Lading",
			{"bol_number": dct_payload_data.get("master_data").get("bol_number")},
			"name"
		)
		dbl_doc_version = 1.0
		if existing_bol:
			dbl_doc_version = float(frappe.get_value("Bill of Lading", existing_bol, "document_version")) + 0.1


		dct_default = {
			"document_version": dbl_doc_version,
			"message_status": "Final",
			"message_type": "BillOfLading",
			"document_date": frappe.utils.now_datetime(),
			"create_date_time": frappe.utils.now_datetime(),
			"rated_indicator": "Rated",
			"copy_indicator": "Original",
			"stock_required": "No",
			"movement_type": "PortToPort",           
			"service_type": "FullLoad",
			"transport_mode": "Sea"
		}

		dct_master_data = {k: v for k, v in dct_payload_data.get("master_data").items() if v}
		if dct_master_data.get("port_of_load"):
			dct_master_data["port_of_load_location"] = frappe.get_value("UNLOCODE Locations", dct_master_data["port_of_load"], "location_name")
			dct_master_data["port_of_load"] = frappe.get_value("UNLOCODE Locations", dct_master_data["port_of_load"], "locode")
		if dct_master_data.get("port_of_discharge"):
			dct_master_data["port_of_discharge_location"] = frappe.get_value("UNLOCODE Locations", dct_master_data["port_of_discharge"], "location_name")
			dct_master_data["port_of_discharge"] = frappe.get_value("UNLOCODE Locations", dct_master_data["port_of_discharge"], "locode")
		if dct_master_data.get("place_of_receipt"):
			dct_master_data["place_of_receipt_location"] = frappe.get_value("UNLOCODE Locations", dct_master_data["place_of_receipt"], "location_name")
			dct_master_data["place_of_receipt"] = frappe.get_value("UNLOCODE Locations", dct_master_data["place_of_receipt"], "locode")
		if dct_master_data.get("place_of_delivery"):
			dct_master_data["place_of_delivery_location"] = frappe.get_value("UNLOCODE Locations", dct_master_data["place_of_delivery"], "location_name")
			dct_master_data["place_of_delivery"] = frappe.get_value("UNLOCODE Locations", dct_master_data["place_of_delivery"], "locode")
		
		dct_master_data["carrier_table"] = frappe.get_value("Carrier", {"partyalias":dct_master_data["carrier"]}, "name")
		dct_master_data["created_status"] = "S"

		
		for data in dct_default:
			if not dct_master_data.get(data):
				dct_master_data[data] = dct_default.get(data)

		if existing_bol:
			bol_doc = frappe.get_doc("Bill of Lading", existing_bol)
			bol_doc.update(dct_master_data)
			bol_doc.save(ignore_permissions=True)
		else:
			bol_doc = frappe.new_doc("Bill of Lading")
			bol_doc.update(dct_master_data)
			bol_doc.insert(ignore_permissions=True)

		frappe.db.commit()
		bol_id = bol_doc.name
		if dct_payload_data.get("equipments"):
			for equipment in dct_payload_data.get("equipments"):
				eqp_name = None
				dct_cargo_data = {}
				if equipment.get("name"):
					eqp_name = frappe.get_value("Equipments", equipment.get("name"), "name")
				if not eqp_name:
					filters = {
						"equipment_name": equipment.get("equipment_name"),
						"is_active": 1
					}
					if dct_master_data.get("carrier_booking_number"):
						filters["carrier_booking_number"] = dct_master_data.get("carrier_booking_number")

					eqp_name = frappe.get_value("Equipments", filters, "name")

				
				dct_equp_data = {
					"bill_of_lading_id": bol_id,
					"carrier_booking_number": dct_master_data.get("carrier_booking_number"),
					"equipment_name": equipment.get("equipment_name"),
					"service_type" : dct_master_data.get("service_type"),
					"code_value": equipment.get("code_value"),	
					"supplier_type": "CarrierSupplied",
					"container_type_id": equipment.get("container_type_id"),
					"description": equipment.get("description"),
					"comment": equipment.get("comment"),
					"shipper_seal_number": equipment.get("shipper_seal_number"),
					"carrier_seal_number": equipment.get("carrier_seal_number"),
					"cargo_weight": equipment.get("weight_value") or 0,
					"gross_weight": equipment.get("weight_value") or 0,
					"weight_type": equipment.get("weight_type") or "KGM",
					"weight_value": equipment.get("weight_value") or 0,
					"is_active": 1,
					"count": 1
				}

				if equipment.get("hs_code") and equipment.get("cargo_description"):
					dct_cargo_data = {
							"hs_code": equipment.get("hs_code"),
							"cargo_description": equipment.get("cargo_description"),
							"cargo_gross_weight": equipment.get("weight_value") or 0,
							"net_weight": equipment.get("weight_value") or 0,
							"gross_volume": equipment.get("gross_volume") or 0,
							"weight_type": equipment.get("weight_type") or "KGM",
							"volume_unit": equipment.get("volume_unit") or "MTQ"
						}
				
				if eqp_name:
					doc_eqp = frappe.get_doc("Equipments", eqp_name)
					doc_eqp.update(dct_equp_data)

					doc_eqp.set("cargo", [])
					doc_eqp.append("cargo", dct_cargo_data)

					doc_eqp.save(ignore_permissions=True)
				else:
					equipment_doc = frappe.get_doc({
						"doctype": "Equipments",
						**dct_equp_data
					})
					equipment_doc.append("cargo", dct_cargo_data)
					equipment_doc.insert(ignore_permissions=True)

			frappe.db.commit()
				
		bol_doc.set("parties", [])

		for party in dct_payload_data.get("parties", []):
			if party.get("partner_role") == "Shipper":
				doc_party = frappe.get_doc("Shipper", party.get("name"))
				bol_doc.append("parties", {
					"partner_role": party.get("partner_role"),
					"partner_name": doc_party.get("shipper_name"),
					"partner_identifier": doc_party.get("shipper_code"),
					"contact_name": doc_party.get("contact_name"),
					"telephone": doc_party.get("phone"),
					"email": doc_party.get("email"),
					"address_line": doc_party.get("custom_address")
				})

			elif party.get("partner_role") == "Consignee":
				doc_party = frappe.get_doc("Customer DB", party.get("name"))
				bol_doc.append("parties", {
					"partner_role": party.get("partner_role"),
					"partner_name": doc_party.get("customer_name"),
					"partner_identifier": doc_party.get("inttra_company_id"),
					"contact_name": doc_party.get("contact"),
					"telephone": doc_party.get("phone"),
					"email": doc_party.get("email_id"),
					"address_line": doc_party.get("customer_free_form_address")
				})

			elif party.get("partner_role") == "Carrier":
				doc_party = frappe.get_doc("Carrier", party.get("name"))
				bol_doc.append("parties", {
					"partner_role": party.get("partner_role"),
					"partner_name": doc_party.get("partyname1"),
					"partner_identifier": doc_party.get("inttra_id"),
					"contact_name": doc_party.get("contact_name"),
					"telephone": doc_party.get("phone"),
					"email": doc_party.get("email"),
					"address_line": doc_party.get("address")
				})

			elif party.get("partner_role") == "NotifyParty":
				doc_party = frappe.get_doc("Customer DB", party.get("name"))
				bol_doc.append("parties", {
					"partner_role": party.get("partner_role"),
					"partner_name": doc_party.get("customer_name"),
					"partner_identifier": doc_party.get("inttra_company_id"),
					"contact_name": doc_party.get("contact"),
					"telephone": doc_party.get("phone"),
					"email": doc_party.get("email_id"),
					"address_line": doc_party.get("customer_free_form_address")
				})
		
		bol_doc.set("references", [])
		for ref in dct_payload_data.get("references", []):
			bol_doc.append("references", {
				"reference_type": ref.get("reference_type"),
				"text": ref.get("text")
			})
		
		bol_doc.set("instructions", [])
		for ins in dct_payload_data.get("instructions", []):
			bol_doc.append("instructions", {
				"main_reference": ins.get("main_reference"),
				"comment_type": ins.get("comment_type"),
				"text": ins.get("text")
			})
		

		bol_doc.set("bol_attachments", [])
		for att in dct_payload_data.get("bol_attachments", []):
			bol_doc.append("bol_attachments", {
				"file_url": att.get("file_url"),
				"file_name": att.get("file_name")
			})
		
		bol_doc.update({
			"hs_code": dct_cargo_data.get("hs_code"),
			"hs_code_description": dct_cargo_data.get("cargo_description")
		})
		bol_doc.save(ignore_permissions=True)
		frappe.db.commit()

		

		return {
			"message": "Bill of Lading created successfully",
			"status": "success",
			"status_code": 200,
			"bol_id": bol_doc.name
		}
	except Exception as e:
		frappe.db.rollback()
		frappe.log_error(frappe.get_traceback(), "Error creating Bill of Lading from payload")
		frappe.throw("Failed to create Bill of Lading from payload")
		return {
			"message": "Failed to create Bill of Lading from payload",
			"status": "error",
			"status_code": 500
		}


def safe_text(value, separator=" "):
    """Convert list to joined string, return str or empty string."""
    if isinstance(value, list):
        return separator.join(filter(None, [str(v) for v in value]))
    if isinstance(value, dict):
        return value.get("text") or ""
    return str(value or "").strip()	




def xml_to_dict(element):
    result = {}
    
    """Include attributes if present"""
    if element.attrs:
        result.update({k: v for k, v in element.attrs.items()})
    
    """Extract text content if no child elements"""
    text = element.string.strip() if element.string else None
    if text and not element.attrs:
        return text
    elif text:
        result['text'] = text
    
    """Iterate through child elements"""
    for child in element.find_all(recursive=False):
        child_data = xml_to_dict(child)
        
        """ Handle duplicate tags by grouping them into a list"""
        if child.name in result:
            if isinstance(result[child.name], list):
                result[child.name].append(child_data)
            else:
                result[child.name] = [result[child.name], child_data]
        else:
            result[child.name] = child_data
    
    return result

def convert_xml_to_json(file_path):
    """ Read XML content from file """
    with open(file_path, 'r', encoding='utf-8') as file:
        xml_content = file.read()
    
    soup = BeautifulSoup(xml_content, 'xml')
    root = soup.find()  
    
    result = {root.name: xml_to_dict(root)}
    
    return json.dumps(result, indent=2)





@frappe.whitelist(allow_guest=True)
def process_webbl_pdf_files(dct_files):
	try:
		
		
		for key, value in dct_files.items():
			file_path_xml = frappe.utils.get_site_path("private", "sftp_downloads", key)
			file_path_pdf = frappe.utils.get_site_path("private", "sftp_downloads", value)
			if not os.path.exists(file_path_xml) or not os.path.exists(file_path_pdf):
				frappe.throw(f"Missing files: {key} or {value}")
			
			if key.lower().endswith(".xml"):	
				json_output = convert_xml_to_json(file_path_xml)			
				dct_data = json.loads(json_output)
			
			if not dct_data:
				return{
					"message": "No data found!!"
				}
			temp_data = {}

			data = dct_data.get("bl_cross_reference", {})

			document = data.get("document", {})
			sailing = data.get("sailing", {})
			control = data.get("control", {})
			references = data.get("references", {})
			carrier_booking_number = references.get("carrier_booking_number", {}).get("cbn")

			
			existing_bol = frappe.get_value("Bill of Lading", {"carrier_booking_number": carrier_booking_number}, "name")

			if existing_bol:
				doc = frappe.get_doc("Bill of Lading", existing_bol)
				if doc.bol_attachments:
					count = doc.bol_attachments[-1].get("order") or 0
				else:
					count = 0
				doc.update({
                    "carrier": control.get("sender_id"),
                    "message_status": control.get("document_status").capitalize() if control.get("document_status") else None,
                    "pdf_file_name": value if value.lower().endswith(".pdf") else None,
                    "pdf_file_path": f"/files/{value}" if value.lower().endswith(".pdf") else None,
                    "main_vessel": sailing.get("main_vessel"),
                    "main_voyage": sailing.get("main_voyage"),
                    "main_transport_sail_date": sailing.get("main_transport_sail_date"),
                    "port_of_load": sailing.get("port_of_load"),
                    "port_of_discharge": sailing.get("port_of_discharge"),
                    "document_type": document.get("document_type"),
                    "document_number": document.get("document_number"),
                    "rated_indicator": document.get("rated_indicator"),
                    "copy_indicator": document.get("copy_indicator"),
                    "stock_required": document.get("stock_required"),
                })
				if value.lower().endswith(".pdf"):
					doc.append("bol_attachments", {
						"file_url": f"/files/{value}",
						"file_name": value,
						"order": count + 1
					})

				try:
					if document.get("create_date_time"):
						date_value = parse_date_field({
							"dateValue": document.get("create_date_time"),
							"dateFormat": ""
						})
						doc.create_date_time = date_value
				except:
					pass

				try:
					for ref_key, ref_data in references.items():
						reference_type = ''.join(word.capitalize() for word in ref_key.split('_'))

						reference_value = ""
						if isinstance(ref_data, dict):
							values = list(ref_data.values())
							if values and isinstance(values[0], (str, int, float)):  # Accept primitives only
								reference_value = values[0]
						elif isinstance(ref_data, (str, int, float)):
							reference_value = ref_data

						if not reference_value:
							continue

						existing_child = next((r for r in doc.references if r.reference_type == reference_type), None)

						if existing_child:
							existing_child.text = reference_value
						else:
							doc.append("references", {
								"reference_type": reference_type,
								"text": reference_value
							})
				except:
					pass

				doc.save(ignore_permissions=True)
				frappe.db.commit()
			
			
	except Exception as e:
		frappe.log_error(
            title="WebBL PDF File Processing Error",
            message=frappe.get_traceback()
        )
		raise


@frappe.whitelist()
def multiple_file_upload():
	try:
		bol_id = frappe.form_dict.get("bol_id")

		bol_doc = frappe.get_doc("Bill of Lading", bol_id)

		lst_attachments = frappe.request.files.getlist('attachment')  

		if lst_attachments:
			for file in lst_attachments:
				file_content = file.stream.read()
				file_name = file.filename
				if bol_doc.bol_attachments:
					count = bol_doc.bol_attachments[-1].get("order") or 0
				else:
					count = 0
				file_doc = frappe.get_doc({
					"doctype": "File",
					"file_name": file_name,
					"is_private": 0,
					"attached_to_doctype": "Bill of Lading",  # Change if needed
					"attached_to_name": bol_id,
					"content": file_content,
				}).insert(ignore_permissions=True)
				
				bol_doc.append("bol_attachments", {
					"file_url": file_doc.file_url,
					"file_name": os.path.basename(file_doc.file_url),
					"order":count + 1 if count else 1
				})

			bol_doc.save(ignore_permissions=True)
			frappe.db.commit()
			frappe.response["status"] = "success"
			frappe.response["status_code"] = 200
			frappe.response["message"] = "Files attached successfully."
			return
		else:
			frappe.response["status"] = "error"
			frappe.response["status_code"] = 400
			frappe.response["message"] = "No files provided."
			return

	except Exception as e:
		frappe.log_error(
            title="WebBL PDF File Processing Error",
            message=frappe.get_traceback()
        )
		frappe.response["status"] = "error"
		frappe.response["status_code"] = 500
		frappe.response["message"] = "Failed to attach files."
		return


@frappe.whitelist(allow_guest=True)
def delete_uploaded_file(bol_id, file_name):
	try:
		bol_doc = frappe.get_doc("Bill of Lading", bol_id)
		for attachment in bol_doc.bol_attachments:
			if attachment.file_name == file_name:
				bol_doc.remove(attachment)
				bol_doc.save(ignore_permissions=True)
				frappe.db.commit()
				frappe.response["status"] = "success"
				frappe.response["status_code"] = 200
				frappe.response["message"] = "File deleted successfully."
				return
		frappe.response["status"] = "error"
		frappe.response["status_code"] = 400
		frappe.response["message"] = "File not found."
		return
	except Exception as e:
		frappe.log_error(
            title="WebBL PDF File Processing Error",
            message=frappe.get_traceback()
        )
		frappe.response["status"] = "error"
		frappe.response["status_code"] = 500
		frappe.response["message"] = "Failed to delete file."
		return
	



@frappe.whitelist(allow_guest=True)
def process_webbl_pdf_files_one():
	try:
		
		dct_files = {"CMDUSCL250558727_9829.XML":"CMDUSCL250558727_9829.PDF"}
		for key, value in dct_files.items():
			file_path_xml = frappe.utils.get_site_path("private", "sftp_downloads", key)
			file_path_pdf = frappe.utils.get_site_path("private", "sftp_downloads", value)
			if not os.path.exists(file_path_xml) or not os.path.exists(file_path_pdf):
				frappe.throw(f"Missing files: {key} or {value}")
			
			if key.lower().endswith(".xml"):	
				json_output = convert_xml_to_json(file_path_xml)			
				dct_data = json.loads(json_output)
			
			if not dct_data:
				return{
					"message": "No data found!!"
				}

			data = dct_data.get("bl_cross_reference", {})

			document = data.get("document", {})
			sailing = data.get("sailing", {})
			control = data.get("control", {})
			references = data.get("references", {})
			carrier_booking_number = references.get("carrier_booking_number", {}).get("cbn")

			
			existing_bol = frappe.get_value("Bill of Lading", {"carrier_booking_number": carrier_booking_number}, "name")

			if existing_bol:
				doc = frappe.get_doc("Bill of Lading", existing_bol)

				doc.update({
                    "carrier": control.get("sender_id"),
                    "message_status": control.get("document_status").capitalize() if control.get("document_status") else None,
                    "pdf_file_name": value,
                    "pdf_file_path": file_path_pdf,
                    "main_vessel": sailing.get("main_vessel"),
                    "main_voyage": sailing.get("main_voyage"),
                    "main_transport_sail_date": sailing.get("main_transport_sail_date"),
                    "port_of_load": sailing.get("port_of_load"),
                    "port_of_discharge": sailing.get("port_of_discharge"),
                    "document_type": document.get("document_type"),
                    "document_number": document.get("document_number"),
                    "rated_indicator": document.get("rated_indicator"),
                    "copy_indicator": document.get("copy_indicator"),
                    "stock_required": document.get("stock_required"),
                })

				try:
					if document.get("create_date_time"):
						date_value = parse_date_field({
							"dateValue": document.get("create_date_time"),
							"dateFormat": ""
						})
						doc.create_date_time = date_value
				except:
					pass

				try:
					for ref_key, ref_data in references.items():
						reference_type = ''.join(word.capitalize() for word in ref_key.split('_'))

						reference_value = ""
						if isinstance(ref_data, dict):
							values = list(ref_data.values())
							if values and isinstance(values[0], (str, int, float)):  # Accept primitives only
								reference_value = values[0]
						elif isinstance(ref_data, (str, int, float)):
							reference_value = ref_data

						if not reference_value:
							continue

						existing_child = next((r for r in doc.references if r.reference_type == reference_type), None)

						if existing_child:
							existing_child.text = reference_value
						else:
							doc.append("references", {
								"reference_type": reference_type,
								"text": reference_value
							})
				except:
					pass

				doc.save(ignore_permissions=True)
				frappe.db.commit()
			
		frappe.response["status"] = "Success"	
	except Exception as e:
		frappe.log_error(
            title="WebBL PDF File Processing Error",
            message=frappe.get_traceback()
        )
		raise

@frappe.whitelist(allow_guest=True)
def process_webbl_exml_files_manual():
	"""
	Process a list of webbl XML files, extracting relevant data and creating Bill of Lading documents.
	"""
	
	try:
		data = frappe.request.get_json()
		lst_webbl_files = data.get("files_name")
		response = process_webbl_exml_files(lst_webbl_files)
		if response.get("status") == "success":
			frappe.response["status"] = "Success"
			frappe.response["message"] = "BOL processed successfully"
			# return True
		

	except Exception as e:
		frappe.db.rollback()
		frappe.log_error(frappe.get_traceback(), f"Error processing {lst_webbl_files}")
		frappe.msgprint(f"Failed to process {lst_webbl_files}: {str(e)}")
		frappe.local.response["message"] = f"Failed to process {lst_webbl_files}: {str(e)}"
		return False