{"actions": [], "allow_rename": 1, "autoname": "BL-.###", "creation": "2025-05-29 15:24:05.978561", "doctype": "DocType", "engine": "InnoDB", "field_order": ["master_data_section", "document_identifier", "bol_number", "document_version", "message_status", "message_type", "shipment_id", "carrier_booking_number", "contract_number", "document_date", "shipped_on_board_date", "received_for_shipment", "place_of_receipt", "place_of_receipt_location", "place_of_delivery", "place_of_delivery_location", "column_break_dtcq", "movement_type", "service_type", "transport_mode", "total_equipment", "total_packages", "total_gross_weight", "total_gross_volume", "bol_release_date", "bol_release_location", "export_license_number", "export_license_issue", "export_license_expiry", "freight_payment_location", "freight_payment_date", "section_break_dwhp", "carrier_table", "carrier", "hs_code", "column_break_rhnh", "hs_code_description", "from_pdf_xml_section", "document_type", "document_number", "create_date_time", "rated_indicator", "copy_indicator", "stock_required", "transport_stage", "shipment_declared_amount", "shipment_declared_currency", "xml_file_name", "created_status", "column_break_omwq", "port_of_load", "port_of_load_location", "port_of_discharge", "port_of_discharge_location", "main_transport_sail_date", "main_voyage", "main_vessel", "lloyds_code", "transport_means_type", "transport_means", "letter_of_credit_number", "pdf_section", "pdf_file_name", "column_break_ojnf", "pdf_file_path", "reference_tab", "references", "instructions", "section_break_oukm", "parties", "xml_data_tab", "data_section", "xml_data", "section_break_mstx", "bol_attachments"], "fields": [{"fieldname": "bol_number", "fieldtype": "Data", "in_list_view": 1, "label": "BOL Number", "reqd": 1, "unique": 1}, {"default": "2.0", "fieldname": "document_version", "fieldtype": "Data", "label": "Document Version"}, {"default": "Final", "fieldname": "message_status", "fieldtype": "Data", "label": "Message Status"}, {"default": "BillOfLading", "fieldname": "message_type", "fieldtype": "Data", "label": "Message Type"}, {"fieldname": "shipment_id", "fieldtype": "Data", "label": "Shipment ID"}, {"fieldname": "contract_number", "fieldtype": "Data", "label": "Contract Number"}, {"fieldname": "document_date", "fieldtype": "Datetime", "label": "Document Date"}, {"fieldname": "shipped_on_board_date", "fieldtype": "Datetime", "label": "Shipped On Board Date"}, {"fieldname": "movement_type", "fieldtype": "Data", "label": "Movement Type"}, {"fieldname": "service_type", "fieldtype": "Data", "label": "Service Type"}, {"fieldname": "transport_mode", "fieldtype": "Data", "label": "Transport Mode"}, {"default": "1", "fieldname": "total_equipment", "fieldtype": "Int", "label": "Total Equipment"}, {"fieldname": "total_packages", "fieldtype": "Int", "label": "Total Packages"}, {"fieldname": "total_gross_weight", "fieldtype": "Float", "label": "Total Gross Weight (kg)"}, {"fieldname": "total_gross_volume", "fieldtype": "Float", "label": "Total Gross Volume (m³)"}, {"fieldname": "bol_release_date", "fieldtype": "Date", "label": "BOL Release Date"}, {"fieldname": "column_break_dtcq", "fieldtype": "Column Break"}, {"fieldname": "bol_release_location", "fieldtype": "Data", "label": "BOL Release Location"}, {"fieldname": "master_data_section", "fieldtype": "Section Break", "label": "Master Data"}, {"fieldname": "reference_tab", "fieldtype": "Tab Break", "label": "Reference"}, {"fieldname": "references", "fieldtype": "Table", "label": "References", "options": "Reference Information"}, {"fieldname": "instructions", "fieldtype": "Table", "label": "Instructions", "options": "Instructions"}, {"fieldname": "carrier_booking_number", "fieldtype": "Data", "label": "Carrier Booking Number"}, {"fieldname": "document_type", "fieldtype": "Data", "label": "Document Type"}, {"fieldname": "main_vessel", "fieldtype": "Data", "label": "Main Vessel"}, {"fieldname": "main_voyage", "fieldtype": "Data", "label": " Main Voyage"}, {"fieldname": "main_transport_sail_date", "fieldtype": "Datetime", "label": "Main Transport Sail Date"}, {"fieldname": "port_of_load", "fieldtype": "Data", "label": "Port of Load"}, {"fieldname": "port_of_discharge", "fieldtype": "Data", "label": "Port of Discharge"}, {"fieldname": "from_pdf_xml_section", "fieldtype": "Section Break", "label": "Transportation Details"}, {"fieldname": "column_break_omwq", "fieldtype": "Column Break"}, {"fieldname": "pdf_section", "fieldtype": "Section Break", "label": "PDF"}, {"fieldname": "pdf_file_path", "fieldtype": "Data", "label": "PDF File Path"}, {"fieldname": "pdf_file_name", "fieldtype": "Data", "label": "Pdf File Name"}, {"fieldname": "column_break_ojnf", "fieldtype": "Column Break"}, {"fieldname": "carrier", "fieldtype": "Data", "label": "Carrier"}, {"fieldname": "document_number", "fieldtype": "Data", "label": "Document Number"}, {"fieldname": "create_date_time", "fieldtype": "Datetime", "label": "Create Date Time"}, {"fieldname": "rated_indicator", "fieldtype": "Data", "label": "Rated Indicator"}, {"fieldname": "copy_indicator", "fieldtype": "Data", "label": "Copy Indicator"}, {"fieldname": "stock_required", "fieldtype": "Data", "label": "Stock Required"}, {"fieldname": "section_break_oukm", "fieldtype": "Section Break"}, {"fieldname": "parties", "fieldtype": "Table", "label": "Parties", "options": "Partner Information"}, {"fieldname": "transport_stage", "fieldtype": "Data", "label": "Transport Stage"}, {"fieldname": "lloyds_code", "fieldtype": "Data", "label": "Lloyds Code"}, {"fieldname": "transport_means_type", "fieldtype": "Data", "label": "Transport Means Type"}, {"fieldname": "transport_means", "fieldtype": "Data", "label": "Transport Means"}, {"fieldname": "shipment_declared_amount", "fieldtype": "Data", "label": "Shipment Declared Amount"}, {"fieldname": "shipment_declared_currency", "fieldtype": "Data", "label": "Shipment Declared <PERSON><PERSON><PERSON><PERSON>"}, {"fieldname": "letter_of_credit_number", "fieldtype": "Data", "label": "Letter Of Credit Number"}, {"fieldname": "export_license_number", "fieldtype": "Data", "label": "Export License Number"}, {"fieldname": "export_license_issue", "fieldtype": "Datetime", "label": "Export License Issue"}, {"fieldname": "export_license_expiry", "fieldtype": "Datetime", "label": "Export License Expiry"}, {"fieldname": "received_for_shipment", "fieldtype": "Datetime", "label": "Received For Shipment"}, {"fieldname": "document_identifier", "fieldtype": "Data", "label": "Document Identifier"}, {"fieldname": "freight_payment_location", "fieldtype": "Data", "label": "Freight Payment Location"}, {"fieldname": "freight_payment_date", "fieldtype": "Datetime", "label": "Freight Payment Date"}, {"fieldname": "place_of_receipt", "fieldtype": "Data", "label": "Place Of Receipt"}, {"fieldname": "place_of_delivery", "fieldtype": "Data", "label": "Place Of Delivery"}, {"fieldname": "hs_code", "fieldtype": "Data", "label": "Hs Code"}, {"fieldname": "section_break_dwhp", "fieldtype": "Section Break"}, {"fieldname": "hs_code_description", "fieldtype": "Small Text", "label": "Hs Code Description"}, {"fieldname": "column_break_rhnh", "fieldtype": "Column Break"}, {"fieldname": "place_of_receipt_location", "fieldtype": "Data", "label": "Place Of Receipt Location"}, {"fieldname": "place_of_delivery_location", "fieldtype": "Data", "label": "Place Of Delivery Location"}, {"fieldname": "port_of_load_location", "fieldtype": "Data", "label": "Port of Load Location"}, {"fieldname": "port_of_discharge_location", "fieldtype": "Data", "label": "Port of Discharge Location"}, {"fieldname": "xml_file_name", "fieldtype": "Data", "label": "XML file Name"}, {"fieldname": "xml_data_tab", "fieldtype": "Tab Break", "label": "XML DATA"}, {"fieldname": "data_section", "fieldtype": "Section Break", "label": "Data"}, {"fieldname": "xml_data", "fieldtype": "Text", "label": "XML Data"}, {"fieldname": "section_break_mstx", "fieldtype": "Section Break"}, {"fieldname": "bol_attachments", "fieldtype": "Table", "label": "BOL Attachments", "options": "BOL Attachments"}, {"fieldname": "carrier_table", "fieldtype": "Data", "label": "Carrier Table"}, {"fieldname": "created_status", "fieldtype": "Data", "label": "Created Status"}], "index_web_pages_for_search": 1, "links": [], "modified": "2025-09-19 02:08:08.696886", "modified_by": "Administrator", "module": "Westside", "name": "Bill of Lading", "naming_rule": "Expression (old style)", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": []}