import { Typography } from "@/components/typography";
import { CustomerReportListType } from "@/types/Reports";
import dayjs from "dayjs";
import { FC } from "react";
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from "@/components/ui/table";

interface ReportListTableProps {
  reports: CustomerReportListType[];
}

const formatDate = (dateString: string) => {
  if (!dateString || dateString === "N/A") return "N/A";

  try {
    const date = new Date(dateString);
    const month = date.toLocaleString("default", { month: "short" });
    const day = date.getDate().toString().padStart(2, "0");
    const year = date.getFullYear();

    return `${month}-${day}-${year}`;
  } catch (e) {
    // Fallback to original behavior if parsing fails
    return dateString.split(" ")[0];
  }
};

const ReportListTable: FC<ReportListTableProps> = ({ reports }) => {
  return (
    <div className="bg-white border rounded-lg p-5 shadow-sm">
      {/* Header Information */}
      <div className="mb-4">
        <Typography variant="h4" weight="semibold" className="text-[#191C36]">
          Customer Reports
        </Typography>
      </div>

      {/* Table Layout */}
      <Table className="w-full border-separate border-spacing-y-2">
        <TableHeader>
          <TableRow className="bg-[#D3DAE7] h-[55px]">
            <TableHead className="p-2 text-left font-bold text-[#191C36]">
              Carrier Booking Number
            </TableHead>
            <TableHead className="p-2 text-left font-bold text-[#191C36]">
              BOL Number
            </TableHead>
            <TableHead className="p-2 text-left font-bold text-[#191C36]">
              Docket
            </TableHead>
            <TableHead className="p-2 text-left font-bold text-[#191C36]">
              Revision
            </TableHead>
            <TableHead className="p-2 text-left font-bold text-[#191C36]">
              Carrier
            </TableHead>
            <TableHead className="p-2 text-left font-bold text-[#191C36]">
              Customer
            </TableHead>
            <TableHead className="p-2 text-left font-bold text-[#191C36]">
              Product group
            </TableHead>
            <TableHead className="p-2 text-left font-bold text-[#191C36]">
              Shipment Date
            </TableHead>
            <TableHead className="p-2 text-left font-bold text-[#191C36]">
              ETA
            </TableHead>
            <TableHead className="p-2 text-left font-bold text-[#191C36]">
              No Of Containers
            </TableHead>
            <TableHead className="p-2 text-left font-bold text-[#191C36]">
              Net Wt in Tons
            </TableHead>
            <TableHead className="p-2 text-left font-bold text-[#191C36]">
              Invoice Value
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {reports.length > 0 ? (
            reports.map((report, index) => (
              <TableRow
                key={index}
                className="border border-[#D3DAE7] shadow-sm"
              >
                <TableCell className="p-2">
                  {report?.carrier_booking_number || "N/A"}
                </TableCell>
                <TableCell className="p-2">
                  {report?.bol_number || "N/A"}
                </TableCell>
                <TableCell className="p-2">{report?.docket || "N/A"}</TableCell>
                <TableCell className="p-2">
                  {report?.revision || "N/A"}
                </TableCell>
                <TableCell className="p-2">
                  {report?.carrier || "N/A"}
                </TableCell>
                <TableCell className="p-2">
                  {report?.customer_name || "N/A"}
                </TableCell>
                <TableCell className="p-2">
                  {report?.product_categories || "N/A"}
                </TableCell>
                <TableCell className="p-2">
                  {report?.shipped_on_board_date
                    ? formatDate(
                        dayjs(report.shipped_on_board_date).format("YYYY-MM-DD")
                      )
                    : "N/A"}
                </TableCell>
                <TableCell className="p-2">
                  {report?.ETA
                    ? formatDate(dayjs(report.ETA).format("YYYY-MM-DD"))
                    : "N/A"}
                </TableCell>
                <TableCell className="p-2">
                  {report?.number_of_containers || "N/A"}
                </TableCell>
                <TableCell className="p-2">
                  {report?.net_weight != null &&
                  !isNaN(Number(report.net_weight))
                    ? `${Number(report.net_weight).toFixed(2)}`
                    : "N/A"}
                </TableCell>
                <TableCell className="p-2">
                  {report?.invoice_value || "N/A"}
                </TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={7} className="p-4 text-center text-gray-500">
                {/* No Reports Found */}
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
};

export default ReportListTable;
