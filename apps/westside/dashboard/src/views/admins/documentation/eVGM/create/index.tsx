import React, { useState, useMemo, useEffect } from "react";
import { useForm, FormProvider, useFieldArray } from "react-hook-form";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  CalendarIcon,
  Plus,
  Minus,
  ChevronDown,
  ChevronUp,
  RefreshCw,
  ChevronLast,
  Save,
  FileText,
  Copy,
  Search,
  Check,
  ChevronsUpDown,
  Calendar,
} from "lucide-react";
import { cn } from "@/lib/utils";
import PartiesSection from "../PartiesSection";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DayPicker } from "react-day-picker";
import "react-day-picker/dist/style.css";
import { fetchDataForEvgm, createEvgm } from "@/services/admin/evgm";
import { fetchPackageTypes } from "@/services/admin/common";
import { fetchContractPartyList } from "@/services/admin/common";
import { useMutation, useQuery } from "@tanstack/react-query";
import {
  fecthBasicBookingRequestData,
  fetchSingleBookingData,
  fetchSingleBookingTemplate,
  submitBookingRequest,
  submitEditBookingRequest,
} from "@/services/admin/booking";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { set } from "lodash";

import EVGMPreviewDialog from "../EVGMPreviewDialog";
import { toast } from "sonner";
import { useNavigate, useSearchParams } from "react-router-dom";

export default function CreateEVGMPage() {
  const navigate = useNavigate();
  const [approvalTime, setApprovalTime] = useState("00:00");
  const [approvalOpen, setApprovalOpen] = useState(false);
  const [approvalSelectedDate, setApprovalSelectedDate] = useState<
    Date | undefined
  >();
  const [bookingNumber, setBookingNumber] = useState("");
  const {
    data: initialData,
    error: initialDataError,
    isFetching: initialDataFetching,
  } = useQuery({
    queryKey: ["fetchInitalBookigData"],
    queryFn: fecthBasicBookingRequestData,
    refetchOnWindowFocus: false,
  });
  const {
    data: fetchDataForEvgmData,
    error: DataError,
    isFetching: DataFetching,
    refetch: DataRefetch,
  } = useQuery({
    queryKey: ["fetchDataForEvgm", bookingNumber],
    queryFn: () => fetchDataForEvgm(bookingNumber),
    enabled: !!bookingNumber,
  });

  const containerTypes = initialData?.message?.data?.container_types;

  const methods = useForm({
    defaultValues: {
      responsible_party: { name: "", data: "" },
      authorized_party: { name: "", data: "" },
      shipper: { name: "", data: "" },
      consignee: { name: "", data: "" },
      carrier: { name: "", data: "" },
      booking_number: "",
      approval_signature: "",
      partner_notification_emails: "",
      approval_datetime: "",
      notify_status_update: false,

      equipment_details: [
        {
          equipment_number: "",
          equipment_type_id: "",
          equipment_type: "",
          equiment_gross_weight: "",
          equiment_tare_weight: "",
          verified_gross_mass: "",
          verified_gross_mass_unit: "KGM",
          customs_seal_number: "",
          weight_determination_method: "Method 1",
          weight_determination_date_time: "",
        },
      ],

      controlValue: "", // "responsible" | "authorized"
    },
    shouldUnregister: false,
  });
  const { control, setValue } = methods;
  const {
    fields: equipment_details,
    append,
    remove,
  } = useFieldArray({
    control,
    name: "equipment_details",
  });

  useEffect(() => {
    console.log("test");
    if (!bookingNumber) return;
    setShowAdditional(true);
    console.log(fetchDataForEvgmData);
    // const { shipper, carrier, booking_number, equipment_details } =
    //   fetchDataForEvgmData?.message.data;

    const newValues = {
      shipper: {
        name: fetchDataForEvgmData?.message.data?.shipper?.name,
        data: fetchDataForEvgmData?.message.data?.shipper?.shipper_name,
      },
      carrier: {
        name: fetchDataForEvgmData?.message.data?.carrier?.name,
        data: fetchDataForEvgmData?.message.data?.carrier?.partyname1,
      },
      booking_number:
        fetchDataForEvgmData?.message.data?.booking_number ||
        methods.getValues("booking_number"),
      equipment_details:
        fetchDataForEvgmData?.message.data?.equipment_details?.length > 0
          ? fetchDataForEvgmData?.message.data?.equipment_details.map(
              (item: any) => ({
                equipment_number: item.equipment_number || "",
                equipment_type_id: item.equipment_type_id || "",
                equipment_type: item.equipment_type || "",
                equiment_gross_weight: item.equiment_gross_weight || "",
                equiment_tare_weight: item.equiment_tare_weight || "",
                verified_gross_mass: "",
                verified_gross_mass_unit: "KGM",
                customs_seal_number: item.customs_seal_number || "",
                weight_determination_method: "Method 1",
                weight_determination_date_time: "",
              })
            )
          : [
              {
                equipment_number: "",
                equipment_type_id: "",
                equipment_type: "",
                equiment_gross_weight: "",
                equiment_tare_weight: "",
                verified_gross_mass: "",
                verified_gross_mass_unit: "KGM",
                customs_seal_number: "",
                weight_determination_method: "Method 1",
                weight_determination_date_time: "",
              },
            ],
    };
    methods.reset(newValues);
    // console.log("RESET VALUES:", newValues, methods.getValues());
  }, [bookingNumber, fetchDataForEvgmData]);

  const [showAdditional, setShowAdditional] = useState(false);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewData, setPreviewData] = useState<any>(null);

  const handlePreview = (formData: any) => {
    let partyType = "";
    if (controlValue === "responsible") {
      partyType = "Responsible Party";
    } else if (controlValue === "authorized") {
      partyType = "Authorized Party";
    }
    const payload = {
      ...formData,
      acting_as: partyType,
    };
    setPreviewData(payload);
    setPreviewOpen(true);
  };

  const [submitting, setSubmitting] = useState(false);
  const handleFinalSubmit = async (formData: any) => {
    setSubmitting(true);
    let partyType = "";
    if (controlValue === "responsible") {
      partyType = "Responsible Party";
    } else if (controlValue === "authorized") {
      partyType = "Authorized Party";
    }
    const payload = {
      ...formData,
      acting_as: partyType,
    };
    if (payload.acting_as === "Responsible Party") {
      payload.authorized_party = { name: "", data: "" };
    }
    console.log("🚀 Final Payload:", payload);

    try {
      const response = await createEvgm(payload);
      if (response?.message?.status_code === 200) {
        toast.success(
          response?.message?.message || "eVGM created successfully!"
        );
        setPreviewOpen(false);
        navigate("/dashboard/documentation/eVGM-workspace");
      } else {
        toast.error(
          response?.message?.message ||
            "Failed to create eVGM. Please try again."
        );
      }
    } catch (error) {
      console.error("Error creating eVGM:", error);
      toast.error("Failed to create eVGM. Please try again.");
    } finally {
      setSubmitting(false); // stop loader
    }
  };
  const [controlValue, setControlValue] = useState("responsible");
  console.log("equipment_details1", equipment_details);
  return (
    <div>
      {/* Stepper */}
      {/* <div className="flex items-center gap-6 mt-3 mb-3">
        {["Create eVGM", "Review eVGM", "eVGM Submitted"].map((step, idx) => (
          <React.Fragment key={idx}>
            <div className="flex items-center">
              <div
                className={cn(
                  "w-8 h-8 flex items-center justify-center rounded-full border-2 text-sm font-medium",
                  idx === 0
                    ? "border-black-600 text-black-600"
                    : "border-gray-300 text-gray-400"
                )}
              >
                {idx + 1}
              </div>
              <span
                className={cn(
                  "ml-2 text-sm",
                  idx === 0 ? "text-black-600 font-semibold" : "text-gray-500"
                )}
              >
                {step}
              </span>
            </div> */}
      {/* {idx < 2 && <div className="h-[2px] w-10 bg-gray-300"></div>} */}
      {/* </React.Fragment>
        ))}
      </div> */}
      {/* EVGM submitter info */}
      <div className="flex justify-between items-center  pb-3 mb-4">
        <div className="text-gray-700 font-semibold">
          EVGM Submitter:{" "}
          <span className="font-normal">Westside Exports Llc</span>
        </div>
        <div className="flex gap-4">
          <div>
            <label className="block text-sm font-medium mb-1">Booking #</label>
            <div className="flex">
              <Input
                {...methods.register("booking_number")}
                placeholder="Booking #"
                className="flex-1 rounded-r-none"
              />
              <Button
                type="button"
                variant="secondary"
                onClick={() => {
                  const value = methods.getValues("booking_number");
                  setBookingNumber(value);
                  // DataRefetch();
                }}
                className="rounded-l-none h-11"
              >
                <Search />
              </Button>
            </div>
            <p className="text-red-500 text-sm mt-1">
              {fetchDataForEvgmData?.message?.status_code == 404 &&
                `*` + fetchDataForEvgmData?.message?.message}
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">Acting as</label>
            <Select
              defaultValue="responsible"
              onValueChange={(value) => setControlValue(value)}
            >
              <SelectTrigger className="w-52">
                <SelectValue placeholder="Responsible Party" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="responsible">Responsible Party</SelectItem>
                <SelectItem value="authorized">Authorized Party</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      <FormProvider {...methods}>
        <form onSubmit={methods.handleSubmit(handlePreview)}>
          <section className="mb-4">
            <h2 className="text-lg font-semibold mb-4">
              Step 1: Enter Parties
            </h2>
            <Card>
              {/* <CardHeader>
                <CardTitle className="flex items-center gap-1 ">
                  Parties
                </CardTitle>
              </CardHeader>
              <hr /> */}
              <CardContent>
                <PartiesSection
                  isAmend={false}
                  showAdditional={showAdditional}
                  generalData={initialData?.message?.data}
                  controlValue={controlValue}
                />
              </CardContent>
            </Card>

            {/* Additional Parties */}
            <div className="mt-0 mb-4 border border-gray-200 ">
              <button
                type="button"
                onClick={() => setShowAdditional(!showAdditional)}
                className="w-full flex items-center justify-between px-6 py-4 text-sm font-medium bg-gray-200 hover:bg-gray-200 transition-colors"
              >
                <span className="flex items-center gap-2 underline">
                  {showAdditional ? (
                    <ChevronUp className="h-4 w-4" />
                  ) : (
                    <ChevronDown className="h-4 w-4" />
                  )}
                  Additional Parties
                </span>
              </button>
            </div>
          </section>

          {/* Step 2 */}
          <section className="mt-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-semibold mt-2">
                Step 2: Enter Container Details
              </h2>
              {/* <span className="flex items-center text-sm underline">
                <FileText className="inline-block mr-1" />
                <a>Copy VGM details from spreadsheet ?</a>
              </span> */}
            </div>

            {equipment_details.map((field, index) => (
              <div
                key={field.id}
                className="border rounded-lg bg-white shadow-sm p-4 mb-4 w-full"
              >
                <div className="overflow-x-auto">
                  <div className="flex gap-4 min-w-[1000px] mb-2">
                    {/* Container Number */}
                    <div className="max-w-[120px] flex-shrink-0">
                      <label className="block text-sm font-medium mb-1 lg:whitespace-normal lg:leading-tight">
                        Container #<span className="text-red-500">*</span>
                      </label>
                      <Input
                        {...methods.register(
                          `equipment_details.${index}.equipment_number`,
                          {
                            required: "Container number is required",
                            pattern: {
                              value: /^[A-Za-z]{4}[0-9]{6,7}$/,
                              message:
                                "Invalid Container Number. Must be 4 letters (A–Z) followed by 6 or 7 digits.",
                            },
                          }
                        )}
                        placeholder="Enter Number..."
                      />
                      {methods.formState.errors?.equipment_details?.[index]
                        ?.equipment_number && (
                        <p className="text-red-500 text-xs mt-1">
                          {
                            methods.formState.errors.equipment_details[index]
                              .equipment_number.message
                          }
                        </p>
                      )}
                    </div>

                    {/* Container Type */}
                    <div className="max-w-[220px] flex-shrink-0">
                      <label className="block text-sm font-medium mb-1 lg:whitespace-normal lg:leading-tight">
                        Container Type<span className="text-red-500">*</span>
                      </label>
                      <FormField
                        control={control}
                        name={`equipment_details.${index}.equipment_type`}
                        rules={{ required: "Container type is required" }}
                        render={({ field }) => {
                          const { value, onChange } = field;
                          const [query, setQuery] = useState<string>("");
                          const [open, setOpen] = useState(false);

                          const filteredContainers = useMemo(() => {
                            if (!query) return containerTypes?.slice(0, 50);
                            return containerTypes
                              ?.filter((container) =>
                                container.shortdescription
                                  ?.toLowerCase()
                                  ?.includes(query.toLowerCase())
                              )
                              ?.slice(0, 50);
                          }, [query, containerTypes]);

                          return (
                            <FormItem>
                              <FormControl>
                                <Popover open={open} onOpenChange={setOpen}>
                                  <PopoverTrigger asChild>
                                    <Button
                                      variant="outline"
                                      role="combobox"
                                      aria-expanded={open}
                                      className="w-full justify-between h-11 truncate"
                                    >
                                      <span
                                        className="truncate"
                                        title={
                                          value
                                            ? containerTypes?.find(
                                                (container) =>
                                                  String(container.typecode) ===
                                                  value
                                              )?.shortdescription
                                            : "Select container type..."
                                        }
                                      >
                                        {value
                                          ? containerTypes?.find(
                                              (container) =>
                                                String(container.typecode) ===
                                                value
                                            )?.shortdescription
                                          : "Select container type..."}
                                      </span>
                                      <ChevronsUpDown className="opacity-50 shrink-0 ml-2" />
                                    </Button>
                                  </PopoverTrigger>
                                  <PopoverContent className="w-full p-0">
                                    <Command>
                                      <CommandInput
                                        placeholder="Search Container Type..."
                                        className="h-9"
                                        value={query}
                                        onValueChange={setQuery}
                                      />
                                      <CommandList>
                                        <CommandEmpty>
                                          No Container Type Found.
                                        </CommandEmpty>
                                        <CommandGroup>
                                          {filteredContainers?.map(
                                            (container) => (
                                              <CommandItem
                                                key={container.name}
                                                value={String(
                                                  container.typecode
                                                )}
                                                title={
                                                  container.shortdescription
                                                }
                                                onSelect={() => {
                                                  onChange({
                                                    name: String(
                                                      container.name
                                                    ),
                                                    type: container?.typecode,
                                                  });
                                                  setValue(
                                                    `equipment_details.${index}.equipment_type`,
                                                    String(container.typecode)
                                                  );
                                                  setValue(
                                                    `equipment_details.${index}.equipment_type_id`,
                                                    String(container.name)
                                                  );
                                                  setOpen(false);
                                                }}
                                              >
                                                {container.shortdescription}
                                                <Check
                                                  className={
                                                    value ===
                                                    String(container.typecode)
                                                      ? "ml-auto opacity-100"
                                                      : "ml-auto opacity-0"
                                                  }
                                                />
                                              </CommandItem>
                                            )
                                          )}
                                        </CommandGroup>
                                      </CommandList>
                                    </Command>
                                  </PopoverContent>
                                </Popover>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          );
                        }}
                      />
                    </div>

                    {/* Weight Determination Date/Time */}
                    <div className="max-w-[200px] flex-shrink-0">
                      <label
                        className="block text-sm font-medium mb-1"
                        title="Weight Determination Date/Time"
                      >
                        <span className="flex items-center gap-1">
                          Wt. Determination
                          <Calendar size={12} className="inline" />
                          <span className="text-red-500">*</span>
                        </span>
                      </label>
                      <div
                        onClick={(e) => {
                          const input = e.currentTarget.querySelector(
                            "input"
                          ) as HTMLInputElement;
                          input.showPicker?.();
                          input.focus();
                        }}
                        className="cursor-pointer"
                      >
                        <Input
                          type="datetime-local"
                          {...methods.register(
                            `equipment_details.${index}.weight_determination_date_time`
                          )}
                          required
                        />
                      </div>
                    </div>

                    {/* Cargo Gross Weight */}
                    <div className="max-w-[120px] flex-shrink-0">
                      <label className="block text-sm font-medium mb-1 lg:whitespace-normal lg:leading-tight">
                        Cargo Gross Wt<span className="text-red-500">*</span>
                      </label>
                      <Input
                        {...methods.register(
                          `equipment_details.${index}.equiment_gross_weight`
                        )}
                        placeholder="Weight..."
                        required
                      />
                    </div>

                    {/* Container Tare Weight */}
                    <div className="max-w-[140px] flex-shrink-0">
                      <label className="block text-sm font-medium mb-1 lg:whitespace-normal lg:leading-tight">
                        Container Tare Wt<span className="text-red-500">*</span>
                      </label>
                      <Input
                        {...methods.register(
                          `equipment_details.${index}.equiment_tare_weight`
                        )}
                        placeholder="Weight..."
                        required
                      />
                    </div>

                    {/* Verified Gross Mass */}
                    <div className="max-w-[180px] flex-shrink-0">
                      <label className="block text-sm font-medium mb-1 lg:whitespace-normal lg:leading-tight">
                        Verified Gross Mass
                        <span className="text-red-500">*</span>
                      </label>
                      <div className="flex">
                        <Input
                          {...methods.register(
                            `equipment_details.${index}.verified_gross_mass`
                          )}
                          placeholder="Weight..."
                          required
                        />
                        <Select
                          onValueChange={(val) =>
                            methods.setValue(
                              `equipment_details.${index}.verified_gross_mass_unit`,
                              val
                            )
                          }
                          defaultValue="KGM"
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Kg" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="KGM">Kg</SelectItem>
                            <SelectItem value="LBR">Lb</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    {/* Customs Seal Number */}
                    <div className="max-w-[160px] flex-shrink-0">
                      <label className="block text-sm font-medium mb-1 lg:whitespace-normal lg:leading-tight">
                        Customs Seal #<span className="text-red-500">*</span>
                      </label>
                      <Input
                        {...methods.register(
                          `equipment_details.${index}.customs_seal_number`
                        )}
                        placeholder="Seal No..."
                      />
                    </div>

                    {/* Weight Determination Method */}
                    <div className="max-w-[180px] flex-shrink-0">
                      <label
                        className="block text-sm font-medium mb-1 lg:whitespace-normal lg:leading-tight"
                        title="Weight Determination Method"
                      >
                        Wt. Determination Method
                      </label>
                      <Select
                        onValueChange={(val) =>
                          methods.setValue(
                            `equipment_details.${index}.weight_determination_method`,
                            val
                          )
                        }
                        defaultValue="Method 1"
                      >
                        <SelectTrigger className="h-11">
                          <SelectValue placeholder="Select" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Method 1">Method 1</SelectItem>
                          <SelectItem value="Method 2">Method 2</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex flex-col justify-center gap-2">
                      {index === equipment_details.length - 1 && (
                        <Button
                          variant="outline"
                          size="icon"
                          type="button"
                          onClick={() => append({})}
                          className="border-blue-500 text-blue-500"
                        >
                          <Plus className="w-4 h-4" />
                        </Button>
                      )}
                      {equipment_details.length !== 1 && (
                        <Button
                          variant="outline"
                          size="icon"
                          type="button"
                          onClick={() => remove(index)}
                          className="border-red-500 text-red-500"
                        >
                          <Minus className="w-4 h-4" />
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </section>

          {/* Step 3 */}
          <section className="mt-6">
            <h2 className="text-lg font-semibold mb-4 mt-2">
              Step 3: Enter Approval & Notifications
            </h2>

            <div className="border rounded-lg bg-white shadow-sm">
              {/* First row: three columns */}
              <div className="grid grid-cols-3 gap-4 p-4 border-b">
                {/* Approval Signature */}
                <div>
                  <label className="block text-sm font-medium mb-1">
                    Approval Signature<span className="text-red-500">*</span>
                  </label>
                  <Input
                    {...methods.register("approval_signature")}
                    placeholder="Enter Name..."
                    required
                  />
                </div>

                {/* Approval Date/Time */}
                {/* <div>
        <label className="block text-sm font-medium mb-1">
          Approval Date/Time<span className="text-red-500">*</span>
        </label>
        <div className="flex items-center gap-2">
          <Input placeholder="DD/MM/YYYY - 00:00" />
          <CalendarIcon className="text-gray-500 w-5 h-5" />
        </div>
      </div> */}
                <div>
                  {/* <label className="block text-sm font-medium mb-1">
    Approval Date/Time<span className="text-red-500">*</span>
  </label>
  <div className="flex w-full">
    <input
      readOnly
      value={approvalDate ? approvalDate.toLocaleDateString("en-GB") : ""}
      placeholder="DD/MM/YYYY - 00:00"
      className="border border-r-0 rounded-l px-3 py-2 cursor-pointer text-md w-full"
      onClick={() => setApprovalOpen(!approvalOpen)}
    />
    <button
      type="button"
      onClick={() => setApprovalOpen(!approvalOpen)}
      className="border border-l-0 rounded-r px-3 flex items-center justify-center bg-white hover:bg-gray-100 border-l-1"
    >
      <CalendarIcon className="text-gray-500 w-5 h-5" />
    </button>
  </div> */}

                  <div>
                    <label className="block text-sm font-medium mb-1">
                      Approval Date/Time<span className="text-red-500">*</span>
                    </label>
                    <Input
                      type="datetime-local"
                      {...methods.register(`approval_datetime`)}
                      required
                    />
                  </div>
                  {approvalOpen && (
                    <div className="absolute bg-white shadow-lg border mt-2 p-3 rounded z-50">
                      <DayPicker
                        mode="single"
                        selected={approvalSelectedDate}
                        onSelect={setApprovalSelectedDate}
                      />
                      <input
                        type="time"
                        value={approvalTime}
                        onChange={(e) => setApprovalTime(e.target.value)}
                        className="mt-2 border rounded px-2 py-1 w-full"
                        required
                      />
                      <button
                        className="mt-2 w-full bg-blue-500 text-white rounded px-3 py-1"
                        onClick={() => setApprovalOpen(false)}
                      >
                        Done
                      </button>
                    </div>
                  )}
                </div>

                {/* Partner Notification Emails */}
                <div>
                  <label className="block text-sm font-medium mb-1">
                    Partner Notification Emails
                  </label>
                  <Input
                    {...methods.register("partner_notification_emails", {
                      validate: (value) => {
                        if (!value) return true; // allow empty field

                        // if (!value || !value.trim()) {
                        //   return "At least one email is required.";
                        // }
                        const emails = value
                          .split(",")
                          .map((e) => e.trim())
                          .filter(Boolean);

                        if (emails.length > 8) {
                          return "You can specify up to 8 email addresses only.";
                        }

                        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                        const invalid = emails.find(
                          (email) => !emailRegex.test(email)
                        );
                        if (invalid) {
                          return `Invalid email: ${invalid}`;
                        }

                        return true;
                      },
                    })}
                    placeholder="Enter Email..."
                  />
                  {methods.formState.errors.partner_notification_emails && (
                    <p className="text-red-500 text-sm mt-1">
                      {
                        methods.formState.errors.partner_notification_emails
                          .message as string
                      }
                    </p>
                  )}
                  <p className="text-xs text-gray-500 mt-1">
                    (You May Specify Up To Eight (8) Email Addresses Separated
                    By Commas)
                  </p>
                </div>
              </div>

              {/* Second row: checkbox */}
              <div className="flex items-center gap-2 p-4">
                <input
                  id="notifyMe"
                  type="checkbox"
                  className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                  {...methods.register("notify_status_update")}
                />
                <label htmlFor="notifyMe" className="text-sm text-gray-700">
                  Notify me regarding the status and update of this eVGM.
                </label>
              </div>
            </div>
          </section>

          <div className="flex gap-4 mt-6 justify-end">
            {/* Primary Submit */}
            {/* <Button
              type="submit"
              variant="outline"
              className="h-11 px-5 text-gray-700 border-gray-300 hover:bg-orange-500"
            >
              <Save className="mr-2 w-5 h-5" />
              Submit eVGM
            </Button> */}

            {/* Secondary Continue */}
            <Button
              type="submit"
              variant={"secondary"}
              className="h-11 px-5 bg-secondary text-white hover:bg-orange-500"
            >
              Continue To Review
              <ChevronLast className="ml-1 w-5 h-5" />
            </Button>
          </div>
        </form>

        <EVGMPreviewDialog
          open={previewOpen}
          onClose={() => setPreviewOpen(false)}
          previewData={previewData}
          onConfirm={methods.handleSubmit(handleFinalSubmit)}
          submitting={submitting}
        />
      </FormProvider>
    </div>
  );
}
