import React, { useState, useEffect } from "react";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { toast } from "sonner";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import * as Tabs from "@radix-ui/react-tabs";
import {
  PlusCircle,
  Minus,
  FileText,
  X,
  Image,
  ChevronLast,
} from "lucide-react";
import {
  createBillOfLading,
  multipleFileUploadBol,
  readOcrBillOfLading,
  fetchBillOfLadingDetails,
  updateBillOfLading,
} from "@/services/admin/billOfLading";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";

import { useQuery } from "@tanstack/react-query";
import { fecthBasicBookingRequestData } from "@/services/admin/booking";
import { BLComboBox } from "../blComboBox";
import ContainerDetails from "../containerDetails";
import CargoDetails from "../cargoDetails";
import { fetchHsCodes, fetchPackageTypes } from "@/services/admin/common";

const BillOfLadingUpdate = () => {
  const navigate = useNavigate();
  const { id } = useParams();
    const location = useLocation();
  const ocrData = location.state?.ocrData;
  console.log("ocrData from navigation:", ocrData);
  const {
    data: billOfLadingData,
    isLoading: isListLoading,
    refetch: refetchBillOfLading,
  } = useQuery({
    queryKey: ["fetchBillOfLadingDetails", id],
    queryFn: () => fetchBillOfLadingDetails(id),
    enabled: id !== "empty" && !!id,
  });
  // const selectedBillOfLading = billOfLadingData?.message[0] || null;
  const selectedBillOfLading =
  ocrData && Object.keys(ocrData).length > 0
    ? ocrData
    : billOfLadingData?.message[0] || null;

  useEffect(() => {
    if (selectedBillOfLading) {
      const inttraSiNumber = selectedBillOfLading.references?.find(
        (ref) => ref.reference_type === "InttraShippingInstructionId"
      );
      const inttraSiNumberText = inttraSiNumber?.text || "--";

      const shipperReferenceNumber = selectedBillOfLading.references?.find(
        (ref) => ref.reference_type === "ShipperReferenceNumber"
      );
      const shipperReferenceNumberText = shipperReferenceNumber?.text || "--";

      const shipperParty = selectedBillOfLading.parties?.find(
        (party) => party.partner_role === "Shipper"
      );
      const carrierParty = selectedBillOfLading.parties?.find(
        (party) => party.partner_role === "Carrier"
      );
      const consigneeParty = selectedBillOfLading.parties?.find(
        (party) => party.partner_role === "Consignee"
      );
      const notifyparty = selectedBillOfLading.parties?.find(
        (party) => party.partner_role === "NotifyParty"
      );
      const shipperName = shipperParty?.name || "--";
      const carrierName = carrierParty?.name || "--";
      const consigneeName = consigneeParty?.name || "--";
      const notifypartyName = notifyparty?.name || "--";
      setFormData({
        bol_number: selectedBillOfLading.bol_number || "",
        message_status: selectedBillOfLading.message_status || "",
        document_type: selectedBillOfLading.document_type || "",
        issuingOffice: selectedBillOfLading.issuingOffice || "",
        carrier: carrierName || "",
        carrier_booking_number:
          selectedBillOfLading.carrier_booking_number || "",
        shipper: shipperName || "",
        inttra_si_number: inttraSiNumberText || "",
        main_vessel: selectedBillOfLading.main_vessel || "",
        main_voyage: selectedBillOfLading.main_voyage || "",
        sail_date: selectedBillOfLading.sail_date || "",
        place_of_receipt: selectedBillOfLading.place_of_receipt || "",
        place_of_delivery: selectedBillOfLading.place_of_delivery || "",
        port_of_load: selectedBillOfLading.port_of_load || "",
        port_of_discharge: selectedBillOfLading.port_of_discharge || "",
        letter_of_credit_number:
          selectedBillOfLading.letter_of_credit_number || "",
        export_license_issue: selectedBillOfLading.export_license_issue || "",
        export_license_expiry: selectedBillOfLading.export_license_expiry || "",
        total_equipment: selectedBillOfLading.total_equipment || "",
        total_gross_weight: selectedBillOfLading.total_gross_weight || "",
        total_packages: selectedBillOfLading.total_packages || "",
        message_type: selectedBillOfLading.message_type || "",
        consignee: consigneeName || "",
        notifyparty: notifypartyName || "",
        document_version: selectedBillOfLading.document_version || "",
        shipment_id: selectedBillOfLading.shipment_id || "",
        contract_number: selectedBillOfLading.contract_number || "",
        document_date: selectedBillOfLading.document_date || "",
        shipped_on_board_date: selectedBillOfLading.shipped_on_board_date || "",
        movement_type: selectedBillOfLading.movement_type || "",
        service_type: selectedBillOfLading.service_type || "",
        transport_mode: selectedBillOfLading.transport_mode || "",
        total_gross_volume: selectedBillOfLading.total_gross_volume || "",
        bol_release_date: selectedBillOfLading.bol_release_date || "",
        bol_release_location: selectedBillOfLading.bol_release_location || "",
        main_transport_sail_date:
          selectedBillOfLading.main_transport_sail_date || "",
        document_number: selectedBillOfLading.document_number || "",
        create_date_time: selectedBillOfLading.create_date_time || "",
        rated_indicator: selectedBillOfLading.rated_indicator || "",
        copy_indicator: selectedBillOfLading.copy_indicator || "",
        stock_required: selectedBillOfLading.stock_required || "",
        transport_stage: selectedBillOfLading.transport_stage || "",
        lloyds_code: selectedBillOfLading.lloyds_code || "",
        transport_means_type: selectedBillOfLading.transport_means_type || "",
        transport_means: selectedBillOfLading.transport_means || "",
        shipment_declared_amount:
          selectedBillOfLading.shipment_declared_amount || "",
        shipment_declared_currency:
          selectedBillOfLading.shipment_declared_currency || "",
        export_license_number: selectedBillOfLading.export_license_number || "",
        received_for_shipment: selectedBillOfLading.received_for_shipment || "",
        document_identifier: selectedBillOfLading.document_identifier || "",
        freight_payment_location:
          selectedBillOfLading.freight_payment_location || "",
        freight_payment_date: selectedBillOfLading.freight_payment_date || "",
      });

      if (
        selectedBillOfLading.equipment &&
        selectedBillOfLading.equipment.length
      ) {
        setContainers(
          selectedBillOfLading.equipment.map((eq) => ({
            equipment_name: eq.equipment_name || "",
            code_value: eq.code_value || "",
            description: eq.description || "",
            shipper_seal_number: eq.shipper_seal_number || "",
            carrier_seal_number: eq.carrier_seal_number || "",
            weight_value: eq.weight_value || "",
            weight_type: eq.weight_type || "",
            cargo_gross_weight: eq.cargo_weight || "",
            gross_volume: eq.gross_volume || "",
          }))
        );
      }

      // If attachments are already uploaded, you could set them like this
      // (assuming API returns file URLs/names):
      if (selectedBillOfLading.attachments?.length) {
        setAttachments(
          selectedBillOfLading.attachments.map((fileUrl) => {
            // Keep it as placeholder (string), since it's not a File object
            // You may render these separately as "existing attachments"
            return undefined as unknown as File;
          })
        );
      }
    }
  }, [selectedBillOfLading]);

  // form state
  const [formData, setFormData] = useState({
    bol_number: "",
    message_status: "",
    document_type: "",
    issuingOffice: "",
    carrier: "",
    carrier_booking_number: "",
    shipper: "",
    inttra_si_number: "",
    main_vessel: "",
    main_voyage: "",
    sail_date: "",
    place_of_receipt: "",
    place_of_delivery: "",
    port_of_load: "",
    port_of_discharge: "",
    letter_of_credit_number: "",
    export_license_issue: "",
    export_license_expiry: "",
    total_equipment: "",
    total_gross_weight: "",
    total_packages: "",
    message_type: "",
    consignee: "",
    notifyparty: "",
    document_version: "",
    shipment_id: "",
    contract_number: "",
    document_date: "",
    shipped_on_board_date: "",
    movement_type: "",
    service_type: "",
    transport_mode: "",
    total_gross_volume: "",
    bol_release_date: "",
    bol_release_location: "",
    main_transport_sail_date: "",
    document_number: "",
    create_date_time: "",
    rated_indicator: "",
    copy_indicator: "",
    stock_required: "",
    transport_stage: "",
    lloyds_code: "",
    transport_means_type: "",
    transport_means: "",
    shipment_declared_amount: "",
    shipment_declared_currency: "",
    export_license_number: "",
    received_for_shipment: "",
    document_identifier: "",
    freight_payment_location: "",
    freight_payment_date: "",
  });

  const [containers, setContainers] = useState([
    {
      equipment_name: "",
      code_value: "",
      equipment_type: "",
      description: "",
      shipper_seal_number: "",
      carrier_seal_number: "",
      weight_value: "",
      weight_type: "KG",
      cargo_gross_weight: "",
      gross_volume: "",
      gross_volume_unit: "cbm",
    },
  ]);

  const [cargo, setCargo] = useState([
    {
      hs_code: "",
      cargo_description: "",
      package_count: "",
      package_type_description: "",
    },
  ]);

  const [attachments, setAttachments] = useState<File[]>([
    undefined as unknown as File,
  ]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  // OCR popup state
  const [showAdditional, setShowAdditional] = useState(false);

  // const handleOcrUpload = async () => {
  //   if (!ocrFile) {
  //     toast.error("Please upload a file first");
  //     return;
  //   }

  //   setOcrLoading(true); // start loader
  //   try {
  //     const formData1 = new FormData();
  //     formData1.append("file", ocrFile);

  //     console.log("Uploading to OCR:", ocrFile);
  //     const ocrRes = await readOcrBillOfLading(formData1);
  //     console.log("OCR Response:", ocrRes);

  //     if (ocrRes?.status === "success") {
  //       toast.success("File sent successfully.");
  //       setOcrOpen(false);
  //       setOcrFile(null);
  //       ocrRes?.data?.bol_doc_name &&
  //         navigate(
  //           `/dashboard/documentation/my-bill-of-ladding/${ocrRes?.data?.bol_doc_name}`
  //         );
  //     } else {
  //       toast.error("Failed to process OCR. Please try again.");
  //     }
  //   } catch (err) {
  //     toast.error("Error sending file for OCR");
  //   } finally {
  //     setOcrLoading(false);
  //   }
  // };
  const {
    data: initialData,
    error: initialDataError,
    isFetching: initialDataFetching,
  } = useQuery({
    queryKey: ["fetchInitalBookigData"],
    queryFn: fecthBasicBookingRequestData,
    refetchOnWindowFocus: false,
  });

  const { data: packageData } = useQuery({
    queryKey: ["package-types", { search: "" }], // you can pass search state here
    queryFn: fetchPackageTypes,
  });
  const [hsSearch, setHsSearch] = useState("");
   const { data: hsCodeData } = useQuery({
    queryKey: ["hs-codes", { search: hsSearch }],
    queryFn: fetchHsCodes,
  });

  const containerTypes = initialData?.message?.data?.container_types;
  const packageTypes = packageData?.message?.results ?? [];
  const hsCodes = hsCodeData?.message?.results ?? [];

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleContainerChange = (index, e) => {
    const { name, value } = e.target;
    setContainers((prev) => {
      const updated = [...prev];
      updated[index][name] = value;
      return updated;
    });
  };

  const addContainer = () => {
    setContainers((prev) => [
      ...prev,
      {
        equipment_name: "",
        code_value: "",
        equipment_type: "",
        description: "",
        shipper_seal_number: "",
        carrier_seal_number: "",
        weight_value: "",
        weight_type: "",
        cargo_gross_weight: "",
        gross_volume: "",
        gross_volume_unit: "",
      },
    ]);
  };

  const removeContainer = (index) => {
    setContainers((prev) => prev.filter((_, i) => i !== index));
  };
  const handleCargoChange = (index, e) => {
    const { name, value } = e.target;
    setCargo((prev) => {
      const updated = [...prev];
      updated[index][name] = value;
      return updated;
    });
  };

  // add cargo row
  const addCargo = () => {
    setCargo((prev) => [
      ...prev,
      {
        hs_code: "",
        cargo_description: "",
        package_count: "",
        package_type_description: "",
      },
    ]);
  };

  // remove cargo row
  const removeCargo = (index) => {
    setCargo((prev) => prev.filter((_, i) => i !== index));
  };

  const handleAttachmentChange = (e, index) => {
    const file = e.target.files?.[0];
    if (!file) return;
    setAttachments((prev) => {
      const updated = [...prev];
      updated[index] = file;
      return updated;
    });
    e.target.value = "";
  };

  const handleAddAttachment = () => {
    setAttachments((prev) => [...prev, undefined as unknown as File]);
  };
  const [containerErrors, setContainerErrors] = useState<
    {
      equipment_name?: string;
      code_value?: string;
      description?: string;
      weight_value?: string;
      weight_type?: string;
      cargo_gross_weight?: string;
      gross_volume?: string;
    }[]
  >([]);

  // validation function
  const validateContainers = () => {
    const errors = containers.map((c) => {
      const err: {
        equipment_name?: string;
        code_value?: string;
        description?: string;
        weight_value?: string;
        weight_type?: string;
        cargo_gross_weight?: string;
        gross_volume?: string;
      } = {};

      // Container number validation
      if (!c.equipment_name) {
        err.equipment_name = "Container Number is required";
      } else if (!/^[A-Z]{4}\d{6,7}$/.test(c.equipment_name)) {
        err.equipment_name =
          "Invalid Container Number. Must be 4 letters (A–Z) followed by 6 or 7 digits.";
      }

      if (!c.code_value) {
        err.code_value = "Container Type is required";
      }

      // Container description validation
      if (!c.description) {
        err.description = "Container Description is required";
      }

      if (!c.weight_value) {
        err.weight_value = "Container Weight is required";
      }
      if (!c.weight_type) {
        err.weight_type = "Container Weight Type is required";
      }
      // Cargo Gross Weight validation
      if (!c.cargo_gross_weight) {
        err.cargo_gross_weight = "Gross Weight is required";
      }
      if (!c.gross_volume) {
        err.gross_volume = "Gross Volume is required";
      }

      return err;
    });

    setContainerErrors(errors);
    return errors.every((e) => Object.keys(e).length === 0);
  };
  const [cargoErrors, setCargoErrors] = useState<
    {
      hs_code?: string;
      cargo_description?: string;
            package_type_description?: string;
    }[]
  >([]);

  // validation function
  const validateCargo = () => {
    const errors = cargo.map((c) => {
      const err: {
        hs_code?: string;
        cargo_description?: string;
           package_type_description?: string;
      } = {};

      // HS Code validation
      if (!c.hs_code) {
        err.hs_code = "HS Code is required";
      }

      // Cargo Description validation
      if (!c.cargo_description) {
        err.cargo_description = "Cargo Description is required";
      }
      if (!c.package_type_description) {
        err.package_type_description = "Package Type is required";
      }

      return err;
    });

    setCargoErrors(errors);
    return errors.every((e) => Object.keys(e).length === 0);
  };

  const handleSubmit = async () => {
    try {
      setIsSubmitting(true);
      const isContainersValid = validateContainers();
      const isCargoValid = validateCargo();

      if (!isContainersValid || !isCargoValid) {
        // toast.error("Please fix errors before submitting");
        setIsSubmitting(false);
        return;
      }

      const res = await updateBillOfLading({
        ...formData,
        equipment: containers,
        cargo: cargo,
        parties: [
          { partner_role: "Shipper", name: formData.shipper },
          { partner_role: "Consignee", name: formData.consignee },
          { partner_role: "NotifyParty", name: formData.notifyparty },
          { partner_role: "Carrier", name: formData.carrier },
        ],
      });

      if (res?.status_code !== 200) {
        toast.error("Failed to update Bill of Lading");
        return;
      }

      toast.success("Bill of Lading updated successfully");
      const bolId = res?.data?.id;

      if (
        attachments.filter((file) => file instanceof File).length > 0 &&
        bolId
      ) {
        const uploadData = new FormData();
        attachments
          .filter((file) => file instanceof File)
          .forEach((file) => uploadData.append("attachment", file));
        uploadData.append("bol_id", bolId);
        await multipleFileUploadBol(uploadData);
      }

      navigate(-1);
    } catch (err) {
      toast.error("Error updating Bill of Lading");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6 pt-6">
      {/* <h1 className="text-xl font-semibold">Create Bill of Lading</h1> */}
      {/* <div className="flex gap-4 mt-6 justify-end">
        <Button
          type="submit"
          variant={"secondary"}
          className="h-11 px-5 bg-red-600 text-white hover:bg-orange-500 "
          onClick={() => setOcrOpen(true)}
        >
          Read OCR
          <FileText className="ml-1 w-5 h-5" />
        </Button>
      </div> */}

      <div className="p-4 border rounded-lg">
        <Tabs.Root defaultValue="general" className="w-full">
          {/* Tab Headers */}
          <Tabs.List className="flex border-b mb-4">
            <Tabs.Trigger
              value="general"
              className="px-5 py-2 text-sm sm:text-base font-medium text-gray-600 
                 hover:text-gray-900 hover:bg-gray-50 
                 data-[state=active]:text-blue-600 
                 data-[state=active]:border-b-2 
                 data-[state=active]:border-blue-600 
                 transition-colors"
            >
              General Details
            </Tabs.Trigger>

            <Tabs.Trigger
              value="additional"
              className="px-5 py-2 text-sm sm:text-base font-medium text-gray-600 
                 hover:text-gray-900 hover:bg-gray-50 
                 data-[state=active]:text-blue-600 
                 data-[state=active]:border-b-2 
                 data-[state=active]:border-blue-600 
                 transition-colors"
            >
              Additional Details
            </Tabs.Trigger>
          </Tabs.List>

          {/* General Details */}
          <Tabs.Content value="general">
            <div className="grid grid-cols-3 gap-4 mt-4">
              <div>
                <label className="block text-sm font-medium mb-1">
                  BL Number <span className="text-red-500">*</span>
                </label>
                <Input
                  name="bol_number"
                  value={formData.bol_number}
                  onChange={handleChange}
                  placeholder="Enter BL Number"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Status</label>
                <Input
                  name="message_status"
                  value={formData.message_status}
                  onChange={handleChange}
                  placeholder="Enter Status"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">
                  Message Type
                </label>
                <Input
                  name="message_type"
                  value={formData.message_type}
                  onChange={handleChange}
                  placeholder="Enter message type"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">
                  Document Type
                </label>
                <Input
                  name="document_type"
                  value={formData.document_type}
                  onChange={handleChange}
                  placeholder="Enter Document Type"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">
                  Issuing Office
                </label>
                <Input
                  name="issuingOffice"
                  value={formData.issuingOffice}
                  onChange={handleChange}
                  placeholder="Enter Issuing Office"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">
                  Carrier Booking Number<span className="text-red-500">*</span>
                </label>
                <Input
                  name="carrier_booking_number"
                  value={formData.carrier_booking_number}
                  onChange={handleChange}
                  placeholder="Enter Carrier Booking Number"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">
                  INTTRA SI Number <span className="text-red-500">*</span>
                </label>
                <Input
                  name="inttra_si_number"
                  value={formData.inttra_si_number}
                  onChange={handleChange}
                  placeholder="Enter INTTRA SI Number"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">
                  Main Vessel
                </label>
                <Input
                  name="main_vessel"
                  value={formData.main_vessel}
                  onChange={handleChange}
                  placeholder="Enter Main Vessel"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">
                  Main Voyage
                </label>
                <Input
                  name="main_voyage"
                  value={formData.main_voyage}
                  onChange={handleChange}
                  placeholder="Enter Main Voyage"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">
                  Sail Date
                </label>
                <Input
                  type="date"
                  name="sail_date"
                  value={formData.sail_date}
                  onChange={handleChange}
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">
                  Transport Mode
                </label>
                <Input
                  name="transport_mode"
                  value={formData.transport_mode}
                  onChange={handleChange}
                  placeholder="Enter Transport Mode"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">
                  Movement Type
                </label>
                <Input
                  name="movement_type"
                  value={formData.movement_type}
                  onChange={handleChange}
                  placeholder="Enter Movement Type"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">
                  Place of Receipt
                </label>
                <Input
                  name="place_of_receipt"
                  value={formData.place_of_receipt}
                  onChange={handleChange}
                  placeholder="Enter Place of Receipt"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">
                  Place of Delivery
                </label>
                <Input
                  name="place_of_delivery"
                  value={formData.place_of_delivery}
                  onChange={handleChange}
                  placeholder="Enter Place of Delivery"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">
                  Port of Load <span className="text-red-500">*</span>
                </label>
                <Input
                  name="port_of_load"
                  value={formData.port_of_load}
                  onChange={handleChange}
                  placeholder="Enter Port of Load"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">
                  Port of Discharge<span className="text-red-500">*</span>
                </label>
                <Input
                  name="port_of_discharge"
                  value={formData.port_of_discharge}
                  onChange={handleChange}
                  placeholder="Enter Port of Discharge"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">
                  Letter of Credit Ref
                </label>
                <Input
                  name="letter_of_credit_number"
                  value={formData.letter_of_credit_number}
                  onChange={handleChange}
                  placeholder="Enter Letter of Credit Ref"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">
                  Export License Issue Date
                </label>
                <Input
                  type="date"
                  name="export_license_issue"
                  value={formData.export_license_issue}
                  onChange={handleChange}
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">
                  Export License Expiry Date
                </label>
                <Input
                  type="date"
                  name="export_license_expiry"
                  value={formData.export_license_expiry}
                  onChange={handleChange}
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">
                  Total Equipments <span className="text-red-500">*</span>
                </label>
                <Input
                  name="total_equipment"
                  value={formData.total_equipment}
                  onChange={handleChange}
                  placeholder="Enter Total Equipments"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">
                  Total Gross Weight <span className="text-red-500">*</span>
                </label>
                <Input
                  name="total_gross_weight"
                  value={formData.total_gross_weight}
                  onChange={handleChange}
                  placeholder="Enter Total Gross Weight"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">
                  Total Gross Volume
                </label>
                <Input
                  name="total_gross_volume"
                  value={formData.total_gross_volume}
                  onChange={handleChange}
                  placeholder="Enter Total Gross Volume"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">
                  Total Packages
                </label>
                <Input
                  name="total_packages"
                  value={formData.total_packages}
                  onChange={handleChange}
                  placeholder="Enter Total Packages"
                />
              </div>
            </div>
          </Tabs.Content>
          <Tabs.Content value="additional">
            {/* <div className="mt-4">
            <button
              type="button"
              className="text-blue-600 text-sm underline"
              onClick={() => setShowAdditional(!showAdditional)}
            >
              {showAdditional ? "Hide Additional Fields" : "Show Additional Fields"}
            </button>
          </div> */}

            {/* Additional Fields Section */}
            <div className="mt-4 bg-gray-50">
              {/* <h3 className="font-semibold mb-3">Additional Fields</h3> */}
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">
                    Document Version
                  </label>
                  <Input
                    name="document_version"
                    value={formData.document_version}
                    onChange={handleChange}
                    placeholder="Enter Document Version"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    Shipment ID
                  </label>
                  <Input
                    name="shipment_id"
                    value={formData.shipment_id}
                    onChange={handleChange}
                    placeholder="Enter Shipment ID"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    Contract Number
                  </label>
                  <Input
                    name="contract_number"
                    value={formData.contract_number}
                    onChange={handleChange}
                    placeholder="Enter Contract Number"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    Document Date
                  </label>
                  <Input
                    type="datetime-local"
                    name="document_date"
                    value={formData.document_date}
                    onChange={handleChange}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    Shipped on Board Date
                  </label>
                  <Input
                    type="datetime-local"
                    name="shipped_on_board_date"
                    value={formData.shipped_on_board_date}
                    onChange={handleChange}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">
                    Service Type
                  </label>
                  <Input
                    name="service_type"
                    value={formData.service_type}
                    onChange={handleChange}
                    placeholder="Enter Service Type"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    BOL Release Date
                  </label>
                  <Input
                    type="date"
                    name="bol_release_date"
                    value={formData.bol_release_date}
                    onChange={handleChange}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    BOL Release Location
                  </label>
                  <Input
                    name="bol_release_location"
                    value={formData.bol_release_location}
                    onChange={handleChange}
                    placeholder="Enter BOL Release Location"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    Document Number
                  </label>
                  <Input
                    name="document_number"
                    value={formData.document_number}
                    onChange={handleChange}
                    placeholder="Enter Document Number"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    Create Date Time
                  </label>
                  <Input
                    type="datetime-local"
                    name="create_date_time"
                    value={formData.create_date_time}
                    onChange={handleChange}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    Rated Indicator
                  </label>
                  <Input
                    name="rated_indicator"
                    value={formData.rated_indicator}
                    onChange={handleChange}
                    placeholder="Enter Rated Indicator"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    Copy Indicator
                  </label>
                  <Input
                    name="copy_indicator"
                    value={formData.copy_indicator}
                    onChange={handleChange}
                    placeholder="Enter Copy Indicator"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">
                    Lloyds Code
                  </label>
                  <Input
                    name="lloyds_code"
                    value={formData.lloyds_code}
                    onChange={handleChange}
                    placeholder="Enter Lloyds Code"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">
                    Stock Required
                  </label>
                  <Input
                    name="stock_required"
                    value={formData.stock_required}
                    onChange={handleChange}
                    placeholder="Enter Stock Required"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    Transport Stage
                  </label>
                  <Input
                    name="transport_stage"
                    value={formData.transport_stage}
                    onChange={handleChange}
                    placeholder="Enter Transport Stage"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    Main Transport Sail Date
                  </label>
                  <Input
                    type="datetime-local"
                    name="main_transport_sail_date"
                    value={formData.main_transport_sail_date}
                    onChange={handleChange}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    Transport Means Type
                  </label>
                  <Input
                    name="transport_means_type"
                    value={formData.transport_means_type}
                    onChange={handleChange}
                    placeholder="Enter Transport Means Type"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    Transport Means
                  </label>
                  <Input
                    name="transport_means"
                    value={formData.transport_means}
                    onChange={handleChange}
                    placeholder="Enter Transport Means"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    Declared Amount
                  </label>
                  <Input
                    name="shipment_declared_amount"
                    value={formData.shipment_declared_amount}
                    onChange={handleChange}
                    placeholder="Enter Declared Amount"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    Declared Currency
                  </label>
                  <Input
                    name="shipment_declared_currency"
                    value={formData.shipment_declared_currency}
                    onChange={handleChange}
                    placeholder="Enter Declared Currency"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    Export License Number
                  </label>
                  <Input
                    name="export_license_number"
                    value={formData.export_license_number}
                    onChange={handleChange}
                    placeholder="Enter Export License Number"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    Received For Shipment
                  </label>
                  <Input
                    type="datetime-local"
                    name="received_for_shipment"
                    value={formData.received_for_shipment}
                    onChange={handleChange}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    Document Identifier
                  </label>
                  <Input
                    name="document_identifier"
                    value={formData.document_identifier}
                    onChange={handleChange}
                    placeholder="Enter Document Identifier"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    Freight Payment Location
                  </label>
                  <Input
                    name="freight_payment_location"
                    value={formData.freight_payment_location}
                    onChange={handleChange}
                    placeholder="Enter Freight Payment Location"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    Freight Payment Date
                  </label>
                  <Input
                    type="date"
                    name="freight_payment_date"
                    value={formData.freight_payment_date}
                    onChange={handleChange}
                  />
                </div>
              </div>
            </div>
          </Tabs.Content>
        </Tabs.Root>
      </div>

      <div className="p-4 border rounded-lg">
        <h2 className="font-semibold mb-2">Parties</h2>
        <div className="grid grid-cols-3 gap-4">
          <div>
            {/* <label className="block text-sm font-medium mb-1">
                 Carrier <span className="text-red-500">*</span>
               </label> */}
            {/* <Input
                 name="carrier"
                 value={formData.carrier}
                 onChange={handleChange}
                 placeholder="Enter Carrier"
                 required
               /> */}

            <BLComboBox
              label="Carriers"
              required={true}
              value={formData.carrier}
              onChange={(value) => {
                setFormData((prev) => ({
                  ...prev,
                  carrier: value,
                }));
              }}
              options={
                initialData?.message?.data?.carriers?.map((c: any) => ({
                  label: c.partyname1,
                  value: c.name,
                })) ?? []
              }
              placeholder="Select Carrier"
            />
          </div>

          <div>
            {/* <label className="block text-sm font-medium mb-1">
                   Shipper <span className="text-red-500">*</span>
                 </label>
                 <Input
                   name="shipper"
                   value={formData.shipper}
                   onChange={handleChange}
                   placeholder="Enter Shipper"
                   required
                 /> */}
            <BLComboBox
              label="Shipper"
              required={true}
              value={formData.shipper}
              onChange={(value) => {
                setFormData((prev) => ({
                  ...prev,
                  shipper: value,
                }));
              }}
              options={
                initialData?.message?.data?.shippers?.map((c: any) => ({
                  label: c.shipper_name,
                  value: c.name,
                })) ?? []
              }
              placeholder="Select Shipper"
            />
          </div>

          <div>
            <BLComboBox
              label="Consignee"
              required={true}
              value={formData.consignee}
              onChange={(value) => {
                setFormData((prev) => ({
                  ...prev,
                  consignee: value,
                }));
              }}
              options={
                initialData?.message?.data?.customers?.map((c: any) => ({
                  label: c.company_name,
                  value: c.name,
                })) ?? []
              }
              placeholder="Select Consignee"
            />
          </div>
          <div>
            <BLComboBox
              label="Notify Party"
              required={true}
              value={formData.notifyparty}
              onChange={(value) => {
                setFormData((prev) => ({
                  ...prev,
                  notifyparty: value,
                }));
              }}
              options={
                initialData?.message?.data?.notify_party?.map((c: any) => ({
                  label: c.name1,
                  value: c.name,
                })) ?? []
              }
              placeholder="Select Notify Party"
            />
          </div>
        </div>
      </div>

      {/* cargo section */}
      <CargoDetails
        cargo={cargo}
        cargoErrors={cargoErrors}
        handleCargoChange={handleCargoChange}
        addCargo={addCargo}
        removeCargo={removeCargo}
        packageTypes={packageTypes ?? []}
        setCargo={setCargo}
        hsCodes={hsCodes ?? []}
        setHsSearch={setHsSearch}
        hsSearch={hsSearch}
      />
      {/* Container Details */}
      <ContainerDetails
        containers={containers}
        containerTypes={containerTypes ?? []}
        containerErrors={containerErrors}
        handleContainerChange={handleContainerChange}
        handleCargoChange={handleCargoChange}
        setContainers={setContainers}
        addContainer={addContainer}
        removeContainer={removeContainer}
        isEditMode={true}
      />

      {/* File Upload Section */}
      <div className="p-6 bg-gray-50 rounded-lg shadow">
        <h2 className="font-semibold text-lg mb-2">Upload b/l</h2>

        {attachments.map((file, index) => (
          <div key={index} className="flex w-1/2 items-center space-x-2 mb-3">
            <div className="flex flex-grow items-center border border-gray-300 rounded-md overflow-hidden">
              <label className="flex items-center gap-2 px-3 py-3 bg-white border-r border-gray-300 text-sm cursor-pointer text-gray-700">
                <Image className="w-5 h-5 text-gray-600" />
                <span className="font-medium">CHOOSE FILE</span>
                <input
                  type="file"
                  className="hidden"
                  onChange={(e) => handleAttachmentChange(e, index)}
                />
              </label>
              <div className="flex-1 flex items-center justify-between bg-gray-50 px-4 py-2 text-sm">
                <span className="truncate text-gray-700">
                  {file?.name || "Upload Attach File Here...."}
                </span>
                {file && (
                  <button
                    type="button"
                    onClick={() => {
                      const updated = [...attachments];
                      updated[index] = undefined as unknown as File;
                      setAttachments(updated);
                    }}
                    className="text-gray-500 hover:text-red-600 ml-2"
                    title="Remove file"
                  >
                    <X className="w-4 h-4" />
                  </button>
                )}
              </div>
            </div>

            {attachments.length > 1 && (
              <Button
                variant="outline"
                size="icon"
                onClick={() =>
                  setAttachments(attachments.filter((_, i) => i !== index))
                }
              >
                <Minus />
              </Button>
            )}

            {index === attachments.length - 1 && (
              <Button
                variant="outline"
                size="icon"
                onClick={handleAddAttachment}
              >
                <PlusCircle />
              </Button>
            )}
          </div>
        ))}
      </div>

      {/* Actions */}
      <div className="flex justify-end gap-2">
        <Button variant="outline" onClick={() => navigate(-1)}>
          Cancel
        </Button>
        <Button onClick={handleSubmit} disabled={isSubmitting}>
          {isSubmitting ? "Updating..." : "Update BL"}
        </Button>
      </div>
    </div>
  );
};

export default BillOfLadingUpdate;
