import userService from "@/services/user.service";
import { useQuery, type QueryFunctionContext } from "@tanstack/react-query";
import toast from "react-hot-toast";
import { useNavigate, useParams } from "react-router-dom";

import { useEffect, useState } from "react";
import { priorityLevel, projectStatus, ReqTypeDropDown } from "@/lib/options";
import type { CategoryOption } from "@/types/type";
import { useForm, type FieldErrors } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  requirementValidationSchema,
  type RequirementValidationValues,
} from "./requirementValidateSchema";

const useRequirementDetail = () => {
  const { id } = useParams();
  const [startOpen, setStartOpen] = useState(false);
  const [endOpen, setEndOpen] = useState(false);
  const [category, setCategory] = useState<CategoryOption[]>([]);
  const [image, setImage] = useState<File | null>(null);
  const [showReject, setShowReject] = useState(false);

  const navigate = useNavigate();

  // Fetch requirement details
  const fetchRequirementDetail = async ({
    queryKey,
  }: QueryFunctionContext<[string, string | undefined]>) => {
    const [, id] = queryKey;
    if (!id) throw new Error("Requirement ID is missing");

    const res = await userService.viewRequrement(id);
    if (res.statusCode === 200) return res?.data;
    else toast.error(res?.message || "Error fetching requirement");
  };

  const { data, isLoading, refetch } = useQuery({
    queryFn: fetchRequirementDetail,
    queryKey: ["requirementDetails", id],
  });

  // Fetch categories
  const fetchCategory = async () => {
    const res = await userService.getCategories();
    if (res?.statusCode === 200) setCategory(res?.data || []);
  };

  useEffect(() => {
    fetchCategory();
  }, []);

  // React Hook Form
  const form = useForm<RequirementValidationValues>({
    resolver: zodResolver(requirementValidationSchema),
    defaultValues: {
      status: undefined,
      startDate: "",
      endDate: "",
      categoryIds: [],
      priority: "",
      type: "",
      budget: "",
      file: "",
    },
    mode: "onChange",
  });

  const {
    control,
    handleSubmit,
    watch,
    formState: { errors, isSubmitting },
    resetField,
    setValue,
  } = form;

  type ApprovedErrors = Extract<
    RequirementValidationValues,
    { status: "approved" }
  >;
  const approvedErrors = errors as FieldErrors<ApprovedErrors>;

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setImage(file);
      setValue("file", file, { shouldValidate: true });
    }
  };

  // Cancel selection
  // const handleCancel = () => {
  //   resetField("status");
  //   setImage(null);
  // };

  // Cancel selection
  const handleCancel = () => {
    resetField("status"); // resets the status
    setValue("status", undefined, { shouldValidate: true }); // ensure dropdown shows empty
    setImage(null); // clear selected image
    resetField("startDate");
    resetField("endDate");
    resetField("categoryIds");
    resetField("priority");
    resetField("type");
    resetField("budget");
    resetField("file");
  };

  // Submit handler
  const onSubmit = async (data: RequirementValidationValues) => {
    if (!id) return toast.error("ID is missing");

    // If rejected, open confirm modal
    if (data.status === "rejected") {
      setShowReject(true);
      return;
    }

    try {
      const formData = new FormData();
      formData.append("status", data.status!);

      if (data.startDate) formData.append("startDate", data.startDate);
      if (data.endDate) formData.append("endDate", data.endDate);
      console.log("test>>>>>>>>>>", data.endDate);

      if (data.priority) formData.append("priority", data.priority);
      if (data.type) formData.append("type", data.type);
      if (data.budget) formData.append("budget", data.budget);

      if (data.categoryIds && data.categoryIds.length > 0) {
        data.categoryIds.forEach((cat) =>
          formData.append("categoryIds[]", cat)
        );
      }

      if (data.file && data.file instanceof File) {
        formData.append("file", data.file, data.file.name);
      }

      const res = await userService.validateRequirement(id, formData);

      if (res?.statusCode === 200) {
        toast.success("Requirement Approved Successfully");
        navigate("/department-admin/requirement");
        refetch();
      }
    } catch (error) {
      console.error(error);
      toast.error("Something went wrong");
    }
  };

  // Confirm reject handler
  const handleRejectConfirm = async () => {
    if (!id) return;

    try {
      const formData = new FormData();
      formData.append("status", "rejected");

      const res = await userService.validateRequirement(id, formData);

      if (res?.statusCode === 200) {
        toast.success("Requirement Rejected Successfully");
        navigate("/department-admin/requirement");
        refetch();
      }
    } catch (error) {
      console.error(error);
      toast.error("Something went wrong while rejecting");
    } finally {
      setShowReject(false);
    }
  };

  return {
    data,
    isLoading,
    onSubmit,
    handleSubmit,
    errors,
    control,
    startOpen,
    setStartOpen,
    endOpen,
    setEndOpen,
    priorityLevel,
    projectStatus,
    ReqTypeDropDown,
    watch,
    category,
    approvedErrors,
    isSubmitting,
    image,
    setImage,
    handleImageChange,
    handleCancel,
    showReject,
    setShowReject,
    handleRejectConfirm,
  };
};

export default useRequirementDetail;